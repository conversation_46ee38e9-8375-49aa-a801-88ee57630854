<img src="https://static.wixstatic.com/media/92b609_bc5deb44bbd04c75a2d8632382550d67~mv2.png/v1/fill/w_220,h_50,al_c,q_85,usm_0.66_1.00_0.01/92b609_bc5deb44bbd04c75a2d8632382550d67~mv2.webp">

REST API

# Installation

To get started with local development, clone this repository onto your local machine. Then follow the instructions below to set up your local environment with Docker.

## Local Environment with Docker

### Copy template files
Grab your docker-compose file
``` bash
cp tools/compose-nginxfpm.yml docker-compose.yml
```

Grab your .env file
``` bash
cp tools/.env.nginxfpm .env
```
Modify its contents to reflect your local environment

⚠️ *the docker builds will utilize ports 80, 443, 5432, 6379, and 9000 by default. Make sure that these ports are available on your machine or modify them in your .env prior to building.*

### Build Nginx, PHP-FPM, Postgres, and Redis containers
Use docker-compose to build your images and launch your containers
``` bash
WWWGROUP=$(id -g) docker-compose up --build --remove-orphans
```

### Install Application Dependencies
``` bash
docker exec -it api.sh composer install
```

### Setup git hooks
``` bash
git config core.hooksPath .githooks
```

### Generate App Key
``` bash
docker exec -it api.sh php artisan key:generate
```
*note: this key is used for encryption of your session cookies*

### Setup Database Schema
``` bash
docker exec -it api.sh php artisan migrate --seed
```
*note: omit the seed flag to start with an empty database*

### Setup Passport
``` bash
docker exec -it api.sh php artisan passport:install
docker exec -it api.sh php artisan passport:keys
```

### Setup Local NPI registry
Check which latest Full Replacement Monthly NPI File is available (https://download.cms.gov/nppes/NPI_Files.html)
``` bash
docker exec -it api.sh php artisan npi:import-csv --month={lastFileMonth} --year={lastFileYear} {bulkSize}
ex.: docker exec -it api.sh php artisan npi:import-csv --month=june --year=2024 10000
```

### Update NPI records in reletional DB
The command fetches fresh NPI record for every row in npi_records table.\
Note: 
- command skips records with fake NPIs and displays the corresponding error
- command skips records if NPI was't found
``` bash
docker exec -it api.sh php artisan npi:update-npi-records
```

### Create /etc/hosts Entry

#### Windows
1. Open Notepad as Administrator:
   - Right-click on Notepad
   - Select "Run as administrator"
   - Click "Yes" when prompted

2. Open the hosts file:
   - Go to File > Open
   - Navigate to `C:\Windows\System32\drivers\etc\`
   - Change file type filter to "All Files (*.*)"
   - Select the `hosts` file

3. Add the following line:
```
127.0.0.1 api-local.shuttle.health hcp-local.shuttle.health dme-local.shuttle.health mfr-local.shuttle.health sha-local.shuttle.health
```

4. Save the file (File > Save)

#### Linux/MacOS
```bash
127.0.0.1 api-local.shuttle.health hcp-local.shuttle.health dme-local.shuttle.health mfr-local.shuttle.health sha-local.shuttle.health 
```

### Send email using google service account
Details could be found on the package page: https://github.com/synio-wesley/laravel-gmail-service-account-mail-driver \
Next environment variables must be set:
``` yml
MAIL_MAILER=gmail-service-account
GMAIL_SERVICE_ACCOUNT_GOOGLE_APPLICATION_CREDENTIALS=path_to_json_google_key_file.json
```

https://gist.github.com/zenorocha/18b10a14b2deb214dc4ce43a2d2e2992

### Trust Shuttle Health Local Authority

#### Linux (Debian)

1. Install ca-certificates package `sudo apt install ca-certificates`
2. Add CA cert to ca-certificates`sudo cp tools/nginx/ssl/ShuttleLocalAuthority.pem /usr/share/ca-certificates/ShuttleLocalAuthority.crt`
3. Add API specific cert to ca-certificates `sudo cp tools/nginx/ssl/api-local.shuttle.health.crt /usr/share/ca-certificates/`
4. Rebuild the ca-certificates package `dpkg-reconfigure ca-certificates`
   1. When prompted how to handle new certificates, scroll down to `ask` and press enter
   2. Mark `api-local.shuttle.health` and `ShuttleLocalAuthority` for inclusion with the space bar.
   3. Select OK by pressing enter

#### MacOS

1. Open Keychain Access application
2. Drag the certificate files (not keys) into the appropriate keychain (probably _login_ under _Default Keychains_)
3. Scroll down to the newly added `shuttle.health` certificate in your Keychain Access app, and double click it
4. In the pop-up window that opens, change "when using this certificate" (under Trust menu) to Always Trust
5. Close the pop-up window and authorize the changes
6. Repeat steps 1-3 for the `Shuttle Health` certificate

#### Windows

1. Open the Certificate Manager:
   - Press `Windows + R` to open the Run dialog
   - Type `certmgr.msc` and press Enter

2. Import the Authority Certificate:
   - Right-click on "Trusted Root Certification Authorities" > "All Tasks" > "Import"
   - Click "Next" in the Certificate Import Wizard
   - Browse to `tools/nginx/ssl/ShuttleLocalAuthority.pem`
   - Click "Next" and select "Place all certificates in the following store"
   - Ensure "Trusted Root Certification Authorities" is selected
   - Click "Next" and then "Finish"

3. Import the API Certificate:
   - Repeat the same process for `tools/nginx/ssl/api-local.shuttle.health.crt`
   - Place this certificate in the "Personal" store

4. Restart your browser to apply the changes

Note: If you receive any security warnings during the import process, click "Yes" to proceed with the installation.

# Development Notes

## Communication channels

### Available values for environment variable PRIMARY_COMMUNICATIONS_SERVICE are following:
- signal-wire - is used for production
- twilio - additional channel for production but it is not tested yet
- slack - is used on most of testing environments and sands all faxes and SMS to slack
- local - is used for local development and sends faxes and SMS to log file

For testing environment SIGNALWIRE_SIGNATURE is set to `test-dev-signature-sw` because we should have possibility to simulate inbound fax or sms
For preproduction SIGNALWIRE_SIGNATURE could be set to production value when inbound fax and sms should be teted using signal wire

## Testing with Postman

There is a postman account set up with various environments and collections. Ask for access or an export in order to run authorized manual tests while developing.

*You should add the Shuttle Local Authority CA Cert (.pem) in your postman Settings > Certificates > CA Certificates*


## Pull Request Checklist

### Write and Run Test

Ensure that tests have been written to cover the functionality that is implemented in the pull request.

To run the tests:

```
docker exec -it api.sh php artisan test
```

### Fix Linting Errors
We are currently using Laravel Pint for linting our PHP code.

Our Github workflow verifies that the linter passes when pushing to `main` or opening a pull request.

To fix all auto-correctable formatting errors:
```
docker exec -it api.sh ./vendor/bin/pint
```
*Add the flag -v to see the details of the changes. Add --test to run in dry-mode*


### Update Postman

If any changes have been made to the routes or request parameters then user to update the team workspace in Postman.


# Troubleshooting

## Database

### Re-Running Database Migrations

1. Rollback previously migrated schemas `docker exec -it api.sh php artisan migrate:rollback` *(note: you may need to rollback multiple times to get an empty schema)*
2. Migrate forward and seed data `docker exec -it api.sh php artisan migrate --seed`

### Rebuilding Your Database Container

1. Stop all of your docker containers
2. Remove the postgres container `docker rm postgres.sh`
3. Remove the postgres image `docker rmi postgres:sh`
4. Remove the postgres volume `docker volume rm shuttle-health-api_api-sh-fs-postgres`
5. Bring up the container again with docker compose `docker-compose up`
6. Rerun the migrations & seed `docker exec -it api.sh php artisan migrate --seed`

### Manually Create Test Database

There is currently an issue with the create-databases.sh script on Windows machines. Follow these steps to create the test database manually:

1. Log into the local postgres instance `docker exec -it postgres.sh psql -U shuttle_health`
2. Create the user and database via SQL ```CREATE USER test_shuttle_health WITH PASSWORD 'test_shuttle_health';
   CREATE DATABASE test_shuttle_health;
   GRANT ALL PRIVILEGES ON DATABASE test_shuttle_health TO test_shuttle_health;```

# Useful commands

### Run orders seeder for manufacturer dashboard
```
docker-compose exec --env MANUFACTURER_ID=1 api php artisan db:seed --class=OrdersSeeder
```

# Dependencies

## Python 3 and PDFID tool
Link: https://github.com/DidierStevens/DidierStevensSuite/blob/master/pdfid.py

PDFID tool is used to check whether PDF files contains XSS vulnerability
