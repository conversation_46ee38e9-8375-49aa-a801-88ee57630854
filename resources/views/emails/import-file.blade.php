<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>PDF Template</title>
    <style type="text/css">
        * {
            box-sizing: border-box;
        }
        @media print {
            table {
                page-break-after: auto;
            }
            tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }
            td {
                page-break-inside: avoid;
                page-break-after: auto;
            }
        }
        .container {
            width: 100%;
            margin: 36px;
            padding: 24px;
            background: #f7f9fc;
        }
        .text {
            font-size: 18px;
            font-weight: 400;
            line-height: 23.4px;
            text-align: left;
        }
        .button {
            border-radius: 4px;
            background: #1250e0;
            color: #ffffff;
            padding: 8px 12px;
            font-size: 12px;
            font-weight: 600;
            line-height: 15.6px;
            text-align: left;
            text-decoration: none;
        }
        table {
            border-collapse: separate;
            border-spacing: 0 24px;
        }
    </style>
</head>
<body>
<table class="container">
    <tbody>
    <tr>
        <td class="text">Dear {{ $userName }},</td>
    </tr>
    <tr>
        <td class="text">
            {{ $actionText }}
        </td>
    </tr>
    <tr>
        <td class="text">
            To proceed, please log in to your account and follow the
            instructions provided.
        </td>
    </tr>
    <tr>
        <td>
            <a class="button" href="{{ $loginUrl }}" type="button" target="_blank">
                Log In to Your Account
            </a>
        </td>
    </tr>
    <tr>
        <td class="text">
            If you have any questions or need further assistance, feel free to
            reach out to our support team.
        </td>
    </tr>
    <tr>
        <td class="text">Thank you for using our application.</td>
    </tr>
    <tr>
        <td class="text">
            Best regards,<br />
            {{ $distributorName }}<br />
            {{ $distributorPhone }}
        </td>
    </tr>
    </tbody>
</table>
</body>
</html>
