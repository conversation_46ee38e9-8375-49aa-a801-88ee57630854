<?php
use App\Utils\PhoneNumberConverter;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title>PDF Template</title>
    <style type="text/css">
        /*@font-face{*/
        /*  font-family:source sans pro;font-style:normal;font-weight:400;src:local('Source Sans Pro'),url(https://fonts.cdnfonts.com/s/12183/SourceSansPro-Regular.woff) format('truetype')*/
        /*}*/
        /*@font-face{*/
        /*  font-family:source sans pro;font-style:normal;font-weight:600;src:local('Source Sans Pro'),url(https://fonts.cdnfonts.com/s/12183/SourceSansPro-Semibold.woff) format('truetype')*/
        /*}*/
        /** {*/
        /*  box-sizing: border-box;*/
        /*  font-family: 'Source Sans Pro', sans-serif;*/
        /*}*/

        /*@media print*/
        /*  {*/
        /*    table { page-break-after:auto }*/
        /*    tr    { page-break-inside:avoid; page-break-after:auto }*/
        /*    td    { page-break-inside:avoid; page-break-after:auto }*/
        /*  }*/
        table {
            width: 100%;
            color: #101137;
        }

        .header-table .td-container .logo {
            width: 100%;
        }

        .header-table .td-container:first-child {
            width: 20%;
        }

        .header-table .td-container:last-child {
            width: 80%;
        }

        .block-row {
            padding: 0;
            margin: 4px;
        }

        .title {
            font-weight: 600;
        }

        .table-info {
            border-collapse: collapse;
            border: 2px solid #000000;
        }

        .table-info tr td {
            border: 1px solid #000000;
        }

        .info-header {
            padding: 0;
            margin: 0;
            text-align: center;
        }

        .info-cell {
            padding: 0;
            margin: 0;
        }

        .cell-block {
            position: relative;
            width: 100%;
            height: 22px;
        }

        .field-title {
            font-size: 10px;
            width: 100%;
            margin: 0;
        }

        .field-content {
            font-size: 12px;
            font-weight: 600;
            margin: 0;
            position: absolute;
            top: 9px;
            right: 5px;
        }

        .section-block td:nth-child(2) {
            width: 25%;
        }

        .section-block td:last-child {
            width: 35%;
        }

        .legal-table {
            border-collapse: collapse;
        }

        .legal-text {
            font-size: 12px;
        }

        .signature-table {
            border-collapse: collapse;
            border: 1px solid #000000;
        }

        .signature-table .signature-container {
            border-collapse: collapse;
            border: 1px solid #000000;
        }

        .inherit-table .td-1 {
            height: 50px;
            text-align: left;
            vertical-align: top;
        }

        .inherit-table .td-2 {
            height: 50px;
            text-align: right;
            vertical-align: bottom;
        }

        .certificate-table {
            padding-left: 16px;
            padding-right: 16px;
        }

        .certificate-table td {
            font-style: normal;
            line-height: 130%;
            font-weight: 400;
            font-size: 14px;
        }

        .wrapper {
            padding-bottom: 8px;
            margin-bottom: 16px;
            border-bottom: 1px solid #CED0D3;
        }

        .wrapper > tbody > tr > td {
            width: 50%;
            vertical-align: baseline;
        }

        .title-table {
            padding-bottom: 16px;
        }

        .certificate-table .certificate-title {
            font-size: 24px;
            color: #356FF6;
            font-weight: 600;
        }

        .certificate-table .sub-title {
            color: #356FF6;
            font-size: 16px;
            font-weight: 600;
        }

        .content {
            padding: 0;
        }

        .content td {
            padding: 4px 0;
        }

        .content td:first-child {
            font-weight: 600;
        }

        .certificate-signature {
            margin-top: 8px;
        }

        .certificate-signature td {
            border: 1px solid #CED0D3;
            border-radius: 4px;
            text-align: center;
            padding: 10px;
            height: 200px;
        }

        .legal {
            margin-top: 120px;
        }

        .legal td {
            padding-bottom: 12px;
            font-size: 12px;
            line-height: 16px;
            color: #a3a6ab;
        }

        .image {
            width: 100%;
            max-width: 400px;
        }

        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
<main>
    <table class="certificate-table title-table wrapper">
        <tr>
            <td class="certificate-title">
                Certificate of Signature for AOB
            </td>
        </tr>
    </table>
    <table class="certificate-table wrapper">
        <tr>
            <td class="sub-title">Certification Details</td>
        </tr>
        <tr>
            <td>
                <table class="content">
                    <tr>
                        <td>Envelope ID:</td>
                        <td>{{ $envelope->id }}</td>
                    </tr>
                    <tr>
                        <td>Document Type:</td>
                        <td>{{ $requestType }}</td>
                    </tr>
                    <tr>
                        <td>Requestor Name:</td>
                        <td>{{ $requestorName }} ({{ $distributorName }})</td>
                    </tr>
                    <tr>
                        <td>Signer Name:</td>
                        <td>{{ $signerName }}</td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <table class="certificate-table wrapper">
        <tr>
            <td class="sub-title">Signing Event</td>
            <td class="sub-title">TimeStamp</td>
        </tr>
        <tr>
            <td>
                <table class="content">
                    <tr>
                        <td>Status:</td>
                        <td>{{ $status }}</td>
                    </tr>
                    <tr>
                        <td>IP address:</td>
                        <td>{{ $envelope->ip_address }}</td>
                    </tr>
                    <tr>
                        <td>Signer Name:</td>
                        <td>{{ $signerName }}</td>
                    </tr>
                </table>
            </td>
            <td>
                <table class="content">
                    <tr>
                        <td>Signed Date:</td>
                        <td>{{ $envelope->created_at->timezone(config('app.sh_timezone'))->format('m/d/Y') }}</td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <table class="certificate-table">
        <tr>
            <td class="sub-title">Signature Preview</td>
        </tr>
    </table>
    <table class="certificate-table certificate-signature">
        <tr>
            <td>
                @if (!empty($signatureSrc))
                    <img class="certificate-signature image" src="data:image/png;base64,{{ $signatureSrc }}" alt=""/>
                @endif
            </td>
        </tr>
    </table>
    <table class="certificate-table legal">
        <tr>
            <td>
                By providing your electronic signature below and clicking “Submit,” you acknowledge that you have read and understand this document in its entirety, and your electronic signature indicates your consent to be bound by this document.
            </td>
        </tr>
        <tr>
            <td>
                You understand that your electronic signature has the same legal force and effect as signing this document by hand in ink. You will receive a copy of your signed document via email to the email address where you received the initial request for signature. If needed, you can request another copy of this signed document by contacting {{ $distributorName ?? ''  }} at {{ PhoneNumberConverter::formatPhoneNumber($distributorPhone ?? '')  }}
            </td>
        </tr>
    </table>
</main>
</body>
</html>
