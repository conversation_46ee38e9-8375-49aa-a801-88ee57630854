<?php
use App\Utils\PhoneNumberConverter;
?>
<table class="certificate-table title-table wrapper">
    <tr>
        <td class="certificate-title">
            Certificate of Signature for AOB
        </td>
    </tr>
</table>
<table class="certificate-table wrapper">
    <tr>
        <td class="sub-title">Certification Details</td>
    </tr>
    <tr>
        <td>
            <table class="content">
                <tr>
                    <td>Envelope ID:</td>
                    <td>{{ $envelope->id }}</td>
                </tr>
                <tr>
                    <td>Document Type:</td>
                    <td>{{ $requestType }}</td>
                </tr>
                <tr>
                    <td>Requestor Name:</td>
                    <td>{{ $requestorName }} ({{ $distributorName }})</td>
                </tr>
                <tr>
                    <td>Signer Name:</td>
                    <td>{{ $signerName }}</td>
                </tr>
            </table>
        </td>
    </tr>
</table>
<table class="certificate-table wrapper">
    <tr>
        <td class="sub-title">Signing Event</td>
        <td class="sub-title">TimeStamp</td>
    </tr>
    <tr>
        <td>
            <table class="content">
                <tr>
                    <td>Status:</td>
                    <td>{{ $status }}</td>
                </tr>
                <tr>
                    <td>IP address:</td>
                    <td>{{ $envelope->ip_address }}</td>
                </tr>
                <tr>
                    <td>Signer Name:</td>
                    <td>{{ $signerName }}</td>
                </tr>
            </table>
        </td>
        <td>
            <table class="content">
                <tr>
                    <td>Signed Date:</td>
                    <td>{{ $envelope->created_at->timezone(config('app.sh_timezone'))->format('m/d/Y') }}</td>
                </tr>
            </table>
        </td>
    </tr>
</table>
<table class="certificate-table">
    <tr>
        <td class="sub-title">Signature Preview</td>
    </tr>
</table>
<table class="certificate-table certificate-signature">
    <tr>
        <td>
            @if (!empty($signatureSrc))
                <img class="certificate-signature image" src="data:image/png;base64,{{ $signatureSrc }}" alt=""/>
            @endif
        </td>
    </tr>
</table>
<table class="certificate-table legal">
    <tr>
        <td>
            By providing your electronic signature below and clicking “Submit,” you acknowledge that you have read and understand this document in its entirety, and your electronic signature indicates your consent to be bound by this document.
        </td>
    </tr>
    <tr>
        <td>
            You understand that your electronic signature has the same legal force and effect as signing this document by hand in ink. You will receive a copy of your signed document via email to the email address where you received the initial request for signature. If needed, you can request another copy of this signed document by contacting {{ $distributorName ?? ''  }} at {{ PhoneNumberConverter::formatPhoneNumber($distributorPhone ?? '')  }}
        </td>
    </tr>
</table>