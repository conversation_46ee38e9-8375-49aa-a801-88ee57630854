<?php
use App\Enums\ProductQuantityTypeEnum;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>PDF Template</title>
    <style type="text/css">
        /* TODO: add valid font and uncomment @font-face{
          font-family:source sans pro;font-style:normal;font-weight:400;src:local('Source Sans Pro'),url(https://fonts.cdnfonts.com/s/12183/SourceSansPro-Regular.woff) format('truetype')
        }
        @font-face{
          font-family:source sans pro;font-style:normal;font-weight:600;src:local('Source Sans Pro'),url(https://fonts.cdnfonts.com/s/12183/SourceSansPro-Semibold.woff) format('truetype')
        } */
        * {
            box-sizing: border-box;
            font-family: Arial, sans-serif;
            /* font-family: 'Source Sans Pro', sans-serif; */
        }
        .page-break {
            page-break-after: always;
        }
        table {
            width: 100%;
            padding-left: 24px;
            padding-right: 24px;
            color: #101137;
        }
        table th, table td {
            font-style: normal;
            line-height: 130%;
            font-weight: 400;
            font-size: 14px;
        }
        .text-align-right {
            text-align: right!important;
        }
        .header {
            border-bottom: 1px solid #F0F0F0;
            background: transparent;
            color: #000000;
            max-width: 100%;
            padding: 0 22px;
        }
        .header td {
            height: 64px;
            vertical-align: middle;
        }
        .title-table {
            padding: 18px 24px;
            vertical-align: middle;
            text-align: center;
        }
        .title {
            font-size: 18px;
            color: #356FF6;
        }
        .bold {
            font-weight: 600;
        }
        .title-info {
            font-size: 20px;
        }
        .info-table {
            border-bottom: 1px solid #F0F0F0;
        }
        .info-table tr:last-child > td {
            padding-bottom: 12px;
        }
        .info-table td {
            vertical-align: top;
        }
        .info-title > th {
            padding-bottom: 8px;
            font-size: 16px;
            text-align: left;
            color: #356FF6;
        }
        .order-table {
            border-bottom: 1px solid #F0F0F0;
            margin-top: 12px;
        }
        .order-table tr:last-child > td {
            padding-bottom: 18px;
        }
        .order-table th {
            font-size: 16px;
            text-align: left;
        }
        .order-table td {
            text-align: left;
        }
        .diagnosis-table {
            margin-top: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #F0F0F0;
            table-layout: auto !important;
        }
        .diagnosis-row {
            text-align: left;
            border-bottom: 1px solid #000000;
        }
        .medical-table {
            margin-top: 18px;
        }
        .medical-table th, .order-header th {
            text-align: left;
            font-size: 18px;
            color: #356FF6;
            padding-bottom: 12px;
        }
        .medical-div {
            width: 100%;
            margin-top: 18px;
        }
        .medical-div .medical-div-title {
            text-align: left;
            font-weight: 600;
            font-size: 18px;
            color: #356FF6;
            padding-bottom: 12px;
        }
        .medical-content {
            background-color: #E0ECFF;
        }
        .medical-item {
            padding: 16px;
        }
        .medical-item th {
            padding-bottom: 0px;
        }
        .medical-item td {
            text-align: left;
        }
        .medical-agreement td {
            padding-top: 12px;
            color: #6B6F77;
            font-size: 12px;
            font-weight: 400;
        }
        .signature-table {
            margin-bottom: 18px;
        }
        .signature-table td {
            font-weight: 600;
            font-size: 18px;
            vertical-align: bottom;
        }
        .signature {
            position: absolute;
            top: 0;
            right: 10%;
        }
        .icon {
            vertical-align: center;
            padding-top: 4px;
        }
        .icon img {
            height: 14px;
            margin-right: 5px;
        }
        .icon span {
            display: inline-block;
        }
        .product-title {
            font-size: 12px;
            font-weight: 700;
        }
        .product-content {
            font-size: 12px;
            font-weight: 400;
            color: #444444;
        }
        .inherit-table {
            margin: 0;
            margin-top: -18px;
            padding: 0;
            table-layout: auto !important;
            width:100%;
            white-space:nowrap;
        }
        .inherit-table .td-1 {
            width: 165px;
            height: 50px;
        }
        .inherit-table .smaller-td {
            width: 125px;
        }
        .inherit-table .td-2 {
            width: auto;
            height: 50px;
            text-align: center;
            border-bottom: 1px solid black;
        }
        .image {
            width: 100%;
            max-width: 250px;
        }
        td.distributor-address {
            text-align: right;
        }
        .logo {
            width: 100px;
            white-space: nowrap;
        }
        th.align-center, td.align-center {
            /* text-align: center; */
        }
        @page {
            margin-bottom: 110px;
        }
        body {
            background-color: white;
            margin-bottom: 130px;
        }
        footer {
            position: fixed;
            bottom: -55px;
            left: 0px;
            right: 0px;
            height: 215px;
        }
        .bottom-info {
            font-size: 14px;
            color: #6B6F77;
            text-align: center;
        }
        .bottom-info span {
            margin: 0 10px;
        }
    </style>
</head>
<body>
<footer>
    <div class="footer">
        <table class="medical-table">
            <tr class="medical-agreement">
                <td>
                    I certify that I am the physician identified in the “Physician Information” section above and hereby attest that the medical necessity information is true, accurate, and complete to the best of my knowledge. I understand that any falsification, omission, or concealment of material fact may subject me to administrative, civil, or criminal liability. The patient/caregiver is capable and has successfully completed or will be trained on the proper use of the products prescribed on this order.
                </td>
            </tr>
        </table>
        <table class="signature-table">
            <tr>
                <td width="60%">
                    <table class="inherit-table">
                        <tr>
                            <td class="td-1">
                                Physician Signature:
                            </td>
                            <td class="td-2">
                                @if(!empty($signatureSrc))
                                    <img src="{{ $signatureSrc }}" class="image" alt="signature"/>
                                @endif
                            </td>
                        </tr>
                    </table>
                </td>
                <td width="40%">
                    <table class="inherit-table">
                        <tr>
                            <td class="td-1 smaller-td">
                                Signature Date:
                            </td>
                            <td class="td-2">
                                @if(!empty($signatureSrc))
                                    {{ $signatureDate }}
                                @endif
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <table>
            <tr>
                <td class="bottom-info">
                    <span>{{ $distributorName ?? '-' }}</span>
                    <span>@if($distributorAddress)
                        {{ $distributorAddress->address_line_1 }},
                        @if($distributorAddress->address_line_2)
                            {{ $distributorAddress->address_line_2 }},
                        @endif
                        {{ $distributorAddress->city }}, {{ $distributorAddress->state }} {{ $distributorAddress->zip }}
                    @endif</span>
                    <span>NPI: {{ $distributorNpi ??  '-' }}</span>
                </td>
            </tr>
        </table>
    </div>
</footer>
<main>
    <table class="header">
        <tr>
            <td>
                @if(!empty($documentRequestId))
                    <p><b>Request ID:</b> #{{ $documentRequestId }}</p>
                @endif
                <p class="title-info"><b>Complete & Fax To: <span class="title">{{ $distributorFax }}</span></b></p>
            </td>
            <td class="logo">
                @if(!empty($distributorLogo))
                    <img src="{{ $distributorLogo }}" alt="logo" width="100px"/>
                @endif
                <p>NPI: {{ $distributorNpi ??  '-' }}</p>
            </td>
        </tr>
    </table>
    <table class="title-table">
        <tr>
            <td class="title">Written Order / Prescription</td>
        </tr>
    </table>
    <table class="info-table">
        <tr class="info-title">
            <th width="50%">
                Patient Information
            </th>
            <th width="50%">
                Provider Information
            </th>
        </tr>
        <tr>
            <td width="50%">
                {{ $patient->first_name }} {{ $patient->last_name }} </br>
                DOB: {{ $patient->date_of_birth?->format('m-d-Y') }} </br>
                @if($patientAddress)
                    {{ $patientAddress->address_line_1 }} </br>
                    @if($patientAddress->address_line_2)
                        {{ $patientAddress->address_line_2 }} </br>
                    @endif
                    {{ $patientAddress->city }}, {{ $patientAddress->state }} {{ $patientAddress->zip }} </br>
                @endif
                <div class="icon">
                    @if(!empty($patientPhone))
                        <img src="data:image/png;base64,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" />
                        <span>{{ $patientPhone }}</span>
                    @endif
                </div>
            </td>
            <td width="50%">
                {{ $providerUser?->first_name ?? '-' }} {{ $providerUser?->last_name ?? '-' }} </br>
                NPI: {{ $providerUser?->npiRecord?->npi ??  '-' }} </br>
                @if($facilityAddress)
                    {{ $facilityAddress->address_line_1 }} </br>
                    @if($facilityAddress->address_line_2)
                        {{ $facilityAddress->address_line_2 }} </br>
                    @endif
                    {{ $facilityAddress->city }}, {{ $facilityAddress->state }} {{ $facilityAddress->zip }} </br>
                @endif
                <div class="icon">
                    @if(!empty($facilityFax))
                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAbCAYAAACN1PRVAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAEhklEQVRIiZ1Wa2wUVRT+zjx2li7bbUvbbaltgfJsQSyokEjEQPQX+kMJSADRxBIfASORpMkmYNRQH9EfxAQVYoEQUkg0USAqjbw0GFAQsdCCtrQplO0++rCt3ZndmeOP7ZTLdLcPTzLZuWfOPd/5zj3n3CXLsmALEaG7p3fqvtpDX7W1tS9hZoCICACDQSCAARADIDAADOmTmqSitLT4QtVLG57z+TI7IAhZloWkOyCeSLje/2j3752doXlut9arquogJijxeHxSLKb7/P68xurtb1SqimIAADNDsYEAIBKJloVC4TlTcrJbA9XbylVViU0YLJFwv1fzyfVQKDInEomWFRb4G+2sSak2eDwZUUWRDdtIDEjU2Xpxrciy4fFkREV7ZgYzJ8GYOWWUIogTwCnpfIh+JGYGEaU0tiMar+OxABUbKF3EqfROxuLvaMEpEwUSnaXbV1RYcFWVFd01VM02ISVdCkeT0fYQkbV+3eqXky0KFvco9qKnp/eBOx3BB5mZYjHde+Nm8woislJ6HHYALi0pvuh2awO6YWTcvt3xUDyecDts4PfnNWX5fB1kmqZ84rv6t0+d+WlbPJ6YdN+kAIiJQQCYwSACmAlEDGbkZGe17wi8Nds0LfXTPftOtra1LwEgJUknRw0RoGmu/i2vVa1Ufj5/YfMP9acDmqb1LaosPzKRRp41s+yMoij6t8dP1LS2tS/NzvK1z55VdhpC+qLR7ml/N99aXnvgcJ1y9tz5LURkbX29akVJcdElO7+2iK0hnpVYfXc67i4EwC9sWLtx1swZ58SiM01TDuzc1RHt6p4uhcKR2ZM9nvAQEDVca1z1y4XfNlmWJQFAJNo1o/7Hs9sHB2NZ4mE73wmALMtxJ3tJkkwiMsFMCjPLNu1wJFq298tDXzOzPLWwoKGkuOjS53sPHAt2hsqDwc6KjevXvDie9KauVsdszPL5bhcXF1325+c25efn3iQiPPrIooOTPZ7w4sqFdU5WYwHeZ0u4V/oAoKpKbNvWV5YxsyTLyUH85MrlHz7x+GO7VVUZBNKfoS3ieYnfGMwjpn7CNF0J03Q5ohzRb84xJeqdRZQkRpDEuPr6+v073/mgJbBj152uru5pAPD9yVOBd2s+brzacP0Zp4PRbosU30gS42Jm0nXDa5oJlzXE+tdLV9Z3dXVPu/JHw+p07MYjDLACIYDMTG8wUP1mRSKecOdOyWkBgFerNq1qvPHXU4srFx4Zt2eMZEcAKWK3A0DulJzWIZYAgLy83Oa8vNw9//cOG/7OYAnDf4pGNmu6y3MskJR3GjEkCMTuK9WhEh7P5ZiK0QgdE0kAsa7rmX39A/m2M9uh/S4+IqAAzAxQOByd6bTv6x/I13U9EwSmz76o/ebPa01Pu1zqv5qm/TNm2IIsqJh3fN3aZzdfvnJ1zf6DdYeZmbzeySEI6dJ1PdMw4hkLKuYek48erTsRvBucfzcYmm8Yhnfko3sNI35vrd/Td4Yic5YueXj/9NKSi5qm9bXcal02OBjLFveZpqUuqJh7bNPG5zf8B+GigLG1jeXHAAAAAElFTkSuQmCC" />
                        <span>{{ $facilityFax }}</span>
                    @endif
                </div>
                <div class="icon">
                    @if(!empty($facilityPhone))
                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAbCAYAAACN1PRVAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAF0UlEQVRIiZ1Wa0wUVxS+d9j37ux7WUEe1WpBEARCxYIKUWmMsb4qamxif6gggitg1KRJE2xqjNHGRgVLKwWjpSIx1qr4ovWR1sQibxCE5S3Lsqz7mH2x7O7c/tCh47CLpufP3LlzzvnOd+93zx1IkiSgzOWakN69/+CrpubWrEmPR5CUEF+9ZvWqIyKR8BV4T0MIAQghQAhN+wZJkgQQQjCiG114rqyi1mIlwugOCoW8r0izZ6lYjOupJMwnAOCtuYBFIISAtrd/6bkfK2+53W48Omr+/W1bNuZACFB5xS81Q8MjySqlQltUkJuGi0TjMzGiQAMZdDqdkm+Oftdls9vVaakpZVs3r8+DEJIQQuB0OqWnS87/8XJEl6hWq7oO7M9N5fP5FnpCJht/oNQcdq/u4WGb3a5OTkqo2pa1YS+GYSTlKBAILPv27swMDVG3j42NR5dXVtWQJMl6FzMmO+ode/Dor0IMQt/mTZ8VAAAQM4FAIDBp8rJXKOSy/h5tX7rJZI7wt/l0QOaYAsQwDPMJhAKTUCgw+ltvCCEQiYTGBQui7pA+km0YN0bR/agxxSjQMiKEAMZhc5xOp0s2OTkppByZAV6fj93W/nw9xKAvWKXqpoL9WaCCAQAAk0hwnY8kWY3NbVmBAggrEeJwOOW4SGQQi0X6mRRHL5YJiKUvTzsNAQDPGpq30ynTAyxWYrbX6+XJ5dJBDofjCMRgJnYAAIAtTk68JBbjo93d2pVdL3oy/TmxWEGTb9IgZkGBlpPOcGrPWCzW5KerMo4hAOC167UnfT4fmy5fhBAQCoVGFovlHjMYom12RzD1/V3LyRQPhhACSxYnV8qkkmHdqD7u95t3jjGrlcukQwtjom+6XBPSigtVlz0eLzcQC+aYPodBCAGPx7Xn7PpyHZvNcj18/ETT1v58HeOQoi2b1+dLxPhoj7Yv42LVlUqPx8OlVz+TUV0mqLi4GAAAgFiMjymV8r7mlvbPO7u6VycnLfqVx+MRVDIOh+OIjYmqrW9o2T48PJLc29u/PGZB1O03goFutxs3jBvna3v70iGGkTw+z0q1vamiGfTh9Ru3j9X9+fiQQiHrP1iYlyIUCo30wzo2Zog+U3q+zkrYQnk8LqGQywYAgMhKEKF2u0MJAMAghL65cyL/zt65Y4NAwDdPATIV5fV6uSVlFbe12r6MkFnBHUX7c9P4fD5Bv1Jsdkfw1Ws3TrW2dmz0+nxcABBgsVgTEolYl5S4qLqxqXWr0fjqQ6VSoS3cl5MukYhHp64YptlsduWJUyX/mEzmDyIjwuo1ebtXcDgcB/MeM1us4Q6HUwEAgiKRyCDGcX1QEOazOxzys6Xl91+O6BLlMulgoWbPMqlU8tIvGAAAmM2WsBOnSp4ShC0kInz2s/zcXZn81/swbeP9mdPpkpaWVdQODA4tkcmkQ4eK8j+eBkZPYDJbwr8/U/bYZDJHqtWqzoL87AyRSDTuD4DZTxFCYGLCLS754ec7A4NDS9JSU8qYAplWqc1mCz5TWn5PN6qPl8mkQ/vzszMUctnA+0qeIGwhR7490cPmsF1YoLZDveM4bijU5CyfN2/uI7PZEnH85OnGpua2LGYrosfQDWLQByBEPB7PGlRcXAz8tR+63NlstjsxIe6K1WINGxgcTmlubd9kJWyh0R/Nq8MwzEc/S3RQkkRBZT9duDlufDU/fdknZ6ekz/w78rekJEliT+sbd1TX/Fbq9Xp5s9TBnWvXZH4dHxd7DcLXTZrKgxDCLlysvtTQ1LJNpVRoDx/UJL0F9j6GEAI6nT6u4uLlKr1+LBYAgObOiXySmBBfk5gQVyMR43qT2RJec/X62faOrrVCocB4oGBvarBKqfUrkEBXO501SZKs+mdNX9y4dfeolbCFUn5sNtvl8Xj4AADA43IJTd7ulRERYQ0Iof9+Uv+veTwebktrx6bnnS9Wd3VrM+12uwrHcX1sTFRt5sqM4yqloo8q9F9VyEN60jMNrgAAAABJRU5ErkJggg==" />
                        <span>{{ $facilityPhone }}</span>
                    @endif
                </div>
            </td>
        </tr>
    </table>
    <table class="order-table">
        <tr class="order-header">
            <th colspan="2">Order Details</th>
            <th colspan="3" class="text-align-right">Order Date: {{ $signatureDate }}</th>
        </tr>
        <tr>
            <th class="align-center">HCPC</th>
            <th class="align-center">Product Name/Description</th>
            <th class="align-center">Qty & Unit Description</th>
        </tr>
        @foreach ($products as $product)
            @php
                /** @var \App\Models\Product $product */
                $productName = $branded ? $product->name : $product->generic_name;
                $productName = $productName ?: '-';
                $hcpcCodesList = $product->getUniqueHcpcCodesList();

                $duration = $product->default_duration_unit?->toTitle() === 'Lifetime' ? 'Lifetime' : $product->pivot->duration_count . ' ' . $product->default_duration_unit?->toTitle();

                $isQuantityNarrative = $product->quantity_type?->value === ProductQuantityTypeEnum::NARRATIVE->value;
                $narrativeUnit = $product->pivot->narrative_unit?->toTitle() . ' ' . $product->pivot->narrative_measure_count . ' ' . $product->pivot->narrative_measure_unit?->toTitle();

                $measure = $product->pivot->measure_count . ' ' . $product->pivot->measure_unit?->toTitle();
            @endphp

            @include('pdf.cmn.parts.product-row', compact('productName', 'hcpcCodesList', 'isQuantityNarrative', 'narrativeUnit', 'duration', 'measure'))
        @endforeach
        @if($hcpcs)
            @foreach($hcpcs as $hcpc)
                @php
                    /** @var \App\Models\CMSHCPC $hcpc */
                    $productName = $hcpc->product_name;
                    $hcpcCodesList = $hcpc->code;

                    $duration = $hcpc->pivot->duration_unit?->toTitle() === 'Lifetime' ? 'Lifetime' : $hcpc->pivot->duration_count . ' ' . $hcpc->pivot->duration_unit?->toTitle();

                    $isQuantityNarrative = !empty($hcpc->narrative);
                    $narrativeUnit = $hcpc->pivot->narrative_unit?->toTitle() . ' ' . $hcpc->pivot->narrative_measure_count . ' ' . $hcpc->pivot->narrative_measure_unit?->toTitle();

                    $measure = $hcpc->pivot->measure_count . ' ' . $hcpc->pivot->measure_unit?->toTitle();
                @endphp

                @include('pdf.cmn.parts.product-row', compact('productName', 'hcpcCodesList', 'isQuantityNarrative', 'narrativeUnit', 'duration', 'measure'))
            @endforeach
        @endif
    </table>
    <table class="diagnosis-table">
        <tr>
            <td class="title" width="130px">Diagnosis Code:</td>
            <td class="diagnosis-row">{{ implode(', ', $diagnosisCodes)}}</td>
            <td width="50%"></td>
        </tr>
    </table>
    @if($displayMedicalNecessity)
        <div class="page-break"></div>
        <div class="medical-div">
            <div>
                <span class="medical-div-title">
                    Medical Necessity
                </span>
            </div>
            <div class="medical-content">
                <div class="medical-item">
                    @foreach ($mpfResponses as $formName => $responseItems)
                        <div style="text-align: center; font-size: 18px; padding-bottom: 5px;">{{ $formName }}</div>

                        @foreach ($responseItems as $item)
                            <div style="font-size: 16px;">
                                <b>{{ $item['index'] + 1 }}.</b>
                                @if(!empty($item['label']))
                                    {{ $item['label'] }}
                                @endif
                            </div>
                            <div style="margin-left: 20px; font-size: 14px;">
                                @if(empty($item['response']))
                                    -
                                @elseif(is_array($item['response']))
                                    @if ($item['type'] === 'checkbox-group')
                                        @if (!empty($item['custom_options']))
                                            @foreach ($item['custom_options'] as $customOption)
                                                @if (!empty($customOption['name']))
                                                    <i>{{ $customOption['name'] }}</i>
                                                @endif

                                                @foreach ($customOption['options'] as $key => $option)
                                                    @if ($responseItem = searchInMultidimensionalArray($item['response'], 'id', $customOption['id']))
                                                        <i>{{ $option['label'] }}</i>
                                                        <b>{{ $responseItem['values'][$key] ?? '-' }}</b>
                                                    @endif
                                                @endforeach

                                                <br />
                                            @endforeach
                                        @else
                                            @foreach ($item['options'] as $option)
                                                @if (searchInMultidimensionalArray($item['response'], 'id', $option['id']))
                                                    <div>- {{ $option['label'] }}</div>
                                                @endif
                                            @endforeach
                                        @endif
                                    @else
                                        {{ implode('|', $item['response']) }}
                                    @endif
                                @else
                                    <b>{{ $item['response'] }}</b>
                                @endif
                            </div>
                        @endforeach
                    @endforeach
                </div>
            </div>
        </div>
    @endif
</main>
</body>
</html>
