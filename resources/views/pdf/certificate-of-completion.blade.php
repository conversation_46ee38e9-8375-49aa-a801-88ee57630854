<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>PDF Template</title>
    <style type="text/css">
      /* @font-face{
        font-family:source sans pro;font-style:normal;font-weight:400;src:local('Source Sans Pro'),url(https://fonts.cdnfonts.com/s/12183/SourceSansPro-Regular.woff) format('truetype')
      }
      @font-face{
        font-family:source sans pro;font-style:normal;font-weight:600;src:local('Source Sans Pro'),url(https://fonts.cdnfonts.com/s/12183/SourceSansPro-Semibold.woff) format('truetype')
      } */
      * {
        box-sizing: border-box;
        /* font-family: 'Source Sans Pro', sans-serif; */
      }

      @media print
        {
          table { page-break-after:auto }
          tr    { page-break-inside:avoid; page-break-after:auto }
          td    { page-break-inside:avoid; page-break-after:auto }
        }
      body {
        background-color: white;
      }
      table {
        width: 100%;
        padding-left: 24px;
        padding-right: 24px;
        color: #101137;
      }
      table th, table td {
        font-style: normal;
        line-height: 130%;
        font-weight: 400;
        font-size: 14px;
      }
      .wrapper {
        margin-bottom: 16px;
      }
      .wrapper > tbody > tr > td {
        width: 50%;
        vertical-align: baseline;
      }
      .title {
        font-size: 16px;
        font-weight: 600;
      }
      .content {
        padding: 0;
      }
      .content td {
        padding: 4px 0;
      }
      .content td:first-child {
        font-weight: 600;
      }
      .signature {
        margin-top: 8px;
      }
      .signature td {
        border: 1px solid #CED0D3;
        border-radius: 4px;
        text-align: center;
        padding: 10px;
        width: 100%;
      }
      .legal {
        margin-top: 24px;
      }
      .legal td {
        padding-bottom: 12px;
        font-size: 12px;
        line-height: 16px;
      }
      .image {
        width: 100%;
      }
    </style>
  </head>
  <body>
    <main>
      <table class="wrapper">
        <tr>
          <td colspan="2" class="title">Certification Details</td>
        </tr>
        <tr>
          <td>
            <table class="content">
              <tr>
                <td>Envelope ID:</td>
                <td>{{ $envelope->id }}</td>
              </tr>
              @isset($order)
                <tr>
                  <td>Record Type:</td>
                  <td> New Prescription </td>
                </tr>
              @endisset
              <tr>
                <td>Originator Name:</td>
                <td>{{ $originatorName }}</td>
              </tr>
              <tr>
                <td>Originator NPI:</td>
                <td>{{ $originatorNpi }}</td>
              </tr>
              <tr>
                <td>Signer Name:</td>
                <td>{{ $signerName }}</td>
              </tr>
              <tr>
                <td>NPI:</td>
                <td>{{ $signerNpi }}</td>
              </tr>
            </table>
          </td>
          <td />
        </tr>
      </table>
      <table class="wrapper">
        <tr>
          <td class="title">Signing Event</td>
          <td class="title">TimeStamp</td>
        </tr>
        <tr>
          <td>
            <table class="content">
              <tr>
                <td>Status:</td>
                <td> Completed </td>
              </tr>
              <tr>
                <td>IP address:</td>
                <td>{{ $envelope->ip_address }}</td>
              </tr>
              <tr>
                <td>Signer Name:</td>
                <td>{{ $signerName }}</td>
              </tr>
              <tr>
                <td>NPI:</td>
                <td>{{ $signerNpi }}</td>
              </tr>
            </table>
          </td>
          <td>
            <table class="content">
              <tr>
                <td>Signed:</td>
                <td>{{ $envelope->created_at->timezone(config('app.sh_timezone'))->format('m-d-Y') }}</td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
      <table>
        <tr>
          <td class="title">Signature Preview</td>
        </tr>
      </table>
      <table class="signature">
        <tr>
          <td>
            <img class="image signature" src="{{ $signatureSrc }}"  alt="signature"/>
          </td>
        </tr>
      </table>
      <table class="legal">
        <tr>
          <td>
            By selecting Shuttle Health Digital Signature, I agree that the signature will be the electronic representation of my signature for all purposes, when I use them on documents & prescriptions - just the same as the pen and paper signature.
          </td>
        </tr>
        <tr>
          <td>
            I certify that I am the physician identified in the “Physician Information” section above and hereby attest that the medical necessity information is true, accurate, and complete to the best of my knowledge. I understand that any falsification, omission, or concealment of material fact may subject me to administrative, civil, or criminal liability. The patient/caregiver is capable and has successfully completed or will be trained on the proper use of the products prescribed on this order.
          </td>
        </tr>
      </table>
    </main>
  </body>
</html>
