<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Document Request</title>
    <style type="text/css">
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            background-color: white;
            font-family: "Source Sans Pro", sans-serif;
            color: black;
        }

        table {
            width: 100%;
            padding-left: 70px;
            padding-right: 0px;
        }

        .header-table {
            text-align: center;
            margin-bottom: 20px;
            margin-top: 20px;
            border-bottom: 1px solid #cccccc;
            padding-bottom: 10px;
            padding-right: 30px;
        }

        .header-table img {
            width: 100%;
        }

        .address-table {
            margin-bottom: 20px;
        }

        .address-table td {
            font-size: 14px;
            vertical-align: top;
        }

        .address-table .left {
            width: 60%;
        }

        .address-table .right {
            width: 40%;
        }

        .address-table .company-info {
            font-family: "Source Sans Pro", sans-serif;
            font-size: 14px;
            font-weight: 700;
            line-height: 1.4;
        }

        .address-table .website a {
            font-size: 14px;
            color: #206cff;

            text-decoration: underline;
        }

        .address-table .website {
            margin-bottom: 7px;
            display: block;
        }

        .icon {
            vertical-align: middle;
            margin-right: 5px;
        }

        .icon img {
            height: 15px;
        }

        .phones-table {
            margin-bottom: 20px;
        }

        .phones-table td {
            font-size: 14px;
            line-height: 1.5;
        }

        .greeting {
            font-size: 14px;
            font-weight: 700;
            color: #333333;
            margin-bottom: 10px;
        }

        .request-table {
            margin-bottom: 10px;
        }

        .request-table td {
            font-size: 14px;
            line-height: 1.5;
        }

        .items-table tr {
            font-size: 14px;
            line-height: 1.5;
        }

        .items-table .list-item {
            padding-left: 20px;
            position: relative;
            font-size: 14px;
            font-weight: 700;
            line-height: 1.4;
        }

        .items-table .list-item .icon {
            position: absolute;
            left: 0;
            top: 2px;
        }

        .comments-table {
            margin-bottom: 20px;
            margin-top: 20px;
            padding-right: 85px;
        }

        .border {
            border-top: 1px solid #cccccc;
            border-bottom: 1px solid #cccccc;
            padding-top: 20px;
            padding-bottom: 20px;
        }

        .comments-table .title {
            font-size: 14px;
            font-weight: 700;
            color: #333333;
            margin-bottom: 5px;
        }

        .comments-table .message {
            font-size: 13px;
            line-height: 1.4;
        }

        /* Additional styles for comment list */
        .comments-table .message ul {
            padding-left: 28px;
        }

        .comments-table .message ul li {
            margin-bottom: 2px;
            position: relative;
        }

        .comments-table .message ul li strong {
            font-weight: 700;
        }

        .comments-table .message ul li span {
            font-weight: 700;
            color: #000000;
        }

        .fax-to {
            text-align: center;
            margin-top: 30px;
            margin-bottom: 20px;
        }

        .fax-to td {
            font-size: 16px;
            font-weight: 700;
            color: #333333;
        }

        .signature-table td {
            font-size: 14px;
            font-weight: 700;
            line-height: 1.5;
        }
    </style>
</head>

<body>
    <main>
        <!-- Header Section -->
        <table class="header-table">
            <tr>
                <td style="text-align: left; width: 50%">
                    <img src="{{ $distributorLogo }}" alt="logo" style="height: 50px; width: auto" />
                </td>
                <td
                    style="
                            text-align: right;
                            width: 50%;
                            font-size: 14px;
                            font-weight: 700;
                            color: #333333;
                        ">
                    <div style="text-align: left; display: inline-block">
                        <span>
                            <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFOSURBVHgB7Zi9DQIxDIUfPxUNBS3sQHs1LUOwAx0D0DEAC7AAtNQ0bEFDQ0tNLEAgCjtnJ+Sk8ydZd5Kj5F4cO7kAjuM4joFORJtpsHmwCdJzCrYPdoMSSUAVbIG80MdvoBTRFfwz5GcUbPl61kaKwPbrfQVDqIW+AWUkpAj8DpATVSTqCPgHtUU0TQBRS0QTBRDRIvrQEzPIAc86b+mfTWxLBCrIMzRnfHfIUP8LroFFAO2iUmU6Mr4D4hhzTssSoo9fQc8RvMD3PjFg2jQ2iaNxAaWx5AARU4kkKJdOUGIRkPqorRLR6iX0njHrErqj0BKCZeBUeBktTauT2PQz/kXWWwmOFJsYXn1UUJL7OB1DsTJqPU4nwatQaVolIEXFST6WlMQXfK7V1yjDhXNKEYi9OcgJ9+OPHniueJZLCukQ/4VmfhfsDMdxHCcXD3ppM8Lvl7e8AAAAAElFTkSuQmCC"
                                alt="request icon"
                                style="
                                        height: 16px;
                                        vertical-align: middle;
                                        width: auto;
                                    " />
                        </span>
                        Request ID:
                        <div
                            style="
                                    margin-top: 5px;
                                    font-size: 14px;
                                    font-weight: 400;
                                    text-align: right;
                                    margin-left: 27px;
                                ">
                            {{ implode(', ', $documentRequestIds) }}
                        </div>
                    </div>
                </td>
            </tr>
        </table>
        <!-- Address and Contact Section -->
        <table class="address-table">
            <tr>
                <td class="left">
                    <div class="company-info">
                        {{ $distributor->name ?? '' }}<br />
                        @if ($distributorAddress)
                        {{ $distributorAddress->address_line_1 }}, {{ $distributorAddress->address_line_2 }}<br />
                        {{ $distributorAddress->city }}, {{ $distributorAddress->state }},
                        {{ $distributorAddress->zip }}
                        @endif
                    </div>
                    @if ($distributorWebsiteUrl)
                    <span class="website"><a
                            href="http://{{ $distributorWebsiteUrl }}">{{ $distributorWebsiteUrl }}</a></span>
                    @endif

                    <span class="icon">
                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKZSURBVHgB7ZktjxRBEIZfvgRnSNiVewaxJxBAAoI1JIAEiV9H0Eh+A+E3YLF7FoMACwKSW3MCBIIzCFaAoJ9sT6anmZ3tnt3pDkk/SeVqbj6uquutnu45qVAoFAqF/5hzgdfNjD0xdtBxzVtjb5SY84HXPVJ38PDA2A0l5mLgdSPH/+GdO1Cd3JGxT0pIaAK/VAf5wjs3Nfbc+odKTKiEVo7vS+mb40+UmNAEvjq+HyTVObM+yY2UkNAEzhy/TSYnjp+0Cn0qcNjj/GCEJrB0fH+qRDIPlYkYCbk6n1ofuTADjZzrPighFyKuvaz1PA8k8V3r4K/Y3xH8SzX7ZXBClxJA0K+cY/fdkCV4CJUQELDbC9mDh5gEYOEdZw0eYhNYqlkF/GzBQ0wTVxDwzPrM+VmT6JsA+r9mj5mZmDp/KwN9EoBTY3dUL6UvGfusDPRNgNFm+VBJiWqwYj1VYvomAL6UritDP+ySACAbeqBaStw09sXYTyVi1wSALWTVD/TCbcUnwdrqrn0G8lyF3hizlOiCCriLOt7ar419DLh3rnXwLtzPTu/E/tz4nH1UAFb2jyChqhJUhQFadtw317/By97PYBxte86+EgA/CUAaY61nLF8WczWD513CF48/qle4cp5zrBb2JSEXRu6ZmjszZqaF6r3CXM3gF16ADMDEPqcajKdqYZ8VqGCk31nf3T9QmbH92RU80MgkfU91Aq0VCP0u1IdjG8Rj1c3t670t+ChiV6OxIBmW2+83nKciU+3AEBLyQVK8K0gGXY+dc/TJzBr+Va1nIO5BRnxv7ZTQEE28DUb8vrFbiqO1iYfsgU1UmyL6gp6gqbd9S9q4vspRgTZIhsqQyMQeV1Ljrcz/HZJ+rikUCoUw/gIpmHZfZsBauwAAAABJRU5ErkJggg=="
                            alt="phone icon"
                            style="
                                  height: 16px;
                                  vertical-align: middle;
                                  width: auto;
                                  margin-right: -10px;
                                " />
                    </span>
                    {{ $distributorPhone }}<br />
                    <span class="icon">
                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHxSURBVHgB7ZgvUwMxEMUfUERrEBgESEAWZLG1YGv4AAxYLJLBoaFfAFELFl1bZGsQRWDOgsCwb+46vblJrrlN+m8mv5k3k2vvctlNsrsXIBKJRCIebMCPI9Eu/EhEIyjxMeBadIIw9EU90Q8qsgUdHdEZwnEg+oNiJmrQ0cq1B1B4LqMuOs3abdEbKqI1oJFrP8GPrqFPZzax5kQDls3aG+Cyibm59mFPWC2EI9+XU4KblcgY2s6hjBABoBGvSBOdkbJERm9ciraxPOg4Zvtf0afphlrJgxe5ayaqARbLMabLlqugD0PCtBmQL9I4jffQZ1tX6LQm0nWfZNd32TjYPhR9FB+yGVDPtdnhvAfPQd5i6rTH7L1DTGsu4z5clTBaLMudC8VVMSApXA/hiLaYCw2XC4vCyR7ouz64KgaQARSRztcA5ooQn5TOHi+iNYCDvkFaYoSAOUcVqrWbmIkl1OAJHdKBApcZqBt+y3/Mv0OfJxjb21m7afm/FJsB+bDGwXZhpwc/JgY0ZrwnMf1oW0IjeJzVzAHmBeN4yvYA47LP8ggB381y+tl2Q23Gwz2Yl0jZVPtwhYrEb+JlEw2AXynhW4aoS4kx0gNZ8oAwjKFAOwOVD2Hn1af2eP1b9CXaE+3AD3r+BYs/NIhEIpEI8A857EpwQIAolgAAAABJRU5ErkJggg=="
                            alt="print icon"
                            style="
                                  height: 16px;
                                  vertical-align: middle;
                                  width: auto;
                                  margin-right: -10px;
                                " />
                    </span>
                    {{ $distributorFax }}
                </td>
                <td class="right">
                    <div style="margin-bottom: 2px; font-weight: bold">
                        Date:
                        <span
                            style="
                              display: inline-block;
                              width: 227px;
                              border-bottom: 1px solid #000;
                              padding-bottom: 2px;
                              font-weight: normal;
                            ">{{ $documentRequestDate?->format('F j, Y') ?? '-' }}</span>
                    </div>
                    <div style="margin-bottom: 2px; font-weight: bold">
                        Attention:
                        <span
                            style="
                              display: inline-block;
                              width: 196px;
                              border-bottom: 1px solid #000;
                              padding-bottom: 2px;
                              font-weight: normal;
                            ">{{ $providerUser->getFullName() ?? '' }}</span>
                    </div>
                    <div style="margin-bottom: 2px; font-weight: bold">
                        To Fax:
                        <span
                            style="
                              display: inline-block;
                              width: 213px;
                              border-bottom: 1px solid #000;
                              padding-bottom: 2px;
                              font-weight: normal;
                            ">{{ $facilityFax }}</span>
                    </div>
                    <br />
                    <div style="margin-bottom: 2px; font-weight: bold">
                        Patient's Name:
                        <span
                            style="
                              display: inline-block;
                              width: 158px;
                              border-bottom: 1px solid #000;
                              padding-bottom: 2px;
                              font-weight: normal;
                            ">{{ $patient->getFullName() }}</span>
                    </div>
                    <div style="margin-bottom: 2px; font-weight: bold">
                        Date of Birth:
                        <span
                            style="
                              display: inline-block;
                              width: 174px;
                              border-bottom: 1px solid #000;
                              padding-bottom: 2px;
                              font-weight: normal;
                            ">{{ $patient?->date_of_birth?->format('m/d/Y') ?? '-' }}</span>
                    </div>
                </td>
            </tr>
        </table>

        <!-- Greeting -->
        <table class="greeting">
            <tr>
                <td>Dear Healthcare Provider:</td>
            </tr>
        </table>

        <!-- Request Section -->
        <table class="request-table">
            <tr>
                <td>
                    To Process your patient's order, we will require the following
                    documentation:
                </td>
            </tr>
        </table>

        <!-- Items List -->
        <ul
            style="
              padding-left: 100px;
              font-size: 14px;
              font-weight: 700;
              line-height: 1.4;
          ">
            @foreach ($documentRequestsList as $requestData)
            @foreach ($requestData['requestDetails'] as $details)
            <li>{{ $details['text'] }}</li>
            @endforeach
            @endforeach
        </ul>

        <!-- Additional Comments Section -->
        <table class="comments-table">
            <tr>
                <td>
                    <div class="border">
                        <div class="title">Additional Comments:</div>
                        <div class="message">
                            <ul>
                                @foreach ($additionalComments as $comment)
                                <li>
                                    <span>{{ $comment['title'] }}:</span>
                                    {{ $comment['message'] }}
                                </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </td>
            </tr>
        </table>

        <!-- Fax Instruction -->
        <table class="fax-to">
            <tr>
                <td>PLEASE FAX BACK TO {{ $distributorFax }}</td>


            </tr>
        </table>

        <!-- Signature Section -->
        <table class="signature-table">
            <tr>
                <td>
                    {{ $loggedInUser?->getFullName() ?? '-' }}<br />
                    {{ $loggedInUser?->phone ?? ($distributorPhone ?? '-') }}
                </td>
            </tr>
        </table>
    </main>
</body>

</html>