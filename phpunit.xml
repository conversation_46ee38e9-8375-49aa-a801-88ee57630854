<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         displayDetailsOnTestsThatTriggerDeprecations="true"
>
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
    </testsuites>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="APP_DEBUG" value="true"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="DB_CONNECTION" value="pgsql"/>
        <env name="DB_DATABASE" value="test_shuttle_health"/>
        <env name="DB_USERNAME" value="test_shuttle_health"/>
        <env name="DB_PASSWORD" value="test_shuttle_health"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="PULSE_ENABLED" value="false"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
        <env name="API_DEBUGGER_ENABLED" value="false"/>
        <env name="APP_LOG_QUERIES" value="false"/>
        <env name="APP_LOG_REQUEST_URL" value="false"/>
        <env name="APP_LOG_LEVEL" value="notice"/>
        <env name="LOG_CHANNEL" value="single"/>
        <env name="PRIMARY_COMMUNICATIONS_SERVICE" value="local"/>
        <env name="DEFAULT_SEED_PASSWORD" value="Password123!"/>

        <!-- # external services -->
        <env name="ZENDESK_SUBDOMAIN" value="test"/>
        <env name="ZENDESK_USERNAME" value="test"/>
        <env name="ZENDESK_TOKEN" value="test"/>
        <env name="ZENDESK_SSO_SHARED_SECRET" value="test"/>
        <env name="SENTRY_LARAVEL_DSN" value="null"/>
        <env name="P_VERIFY_CLIENT_ID" value="test"/>
        <env name="P_VERIFY_CLIENT_SECRET" value="test"/>
        <env name="TWILIO_ACCOUNT_SID" value="test"/>
        <env name="TWILIO_MESSAGING_SID" value="test"/>
        <env name="TWILIO_AUTH_TOKEN" value="test"/>
        <env name="SIGNALWIRE_PROJECT_ID" value="test"/>
        <env name="SIGNALWIRE_SPACE_URL" value="test"/>
        <env name="SIGNALWIRE_FAX_NUMBER" value="**********"/>
        <env name="SIGNALWIRE_TEXT_NUMBER" value="**********"/>
        <env name="SIGNALWIRE_TOKEN" value="test"/>
        <env name="SIGNALWIRE_SIGNATURE" value="test"/>
        <env name="CHANGE_HEALTHCARE_CLIENT_ID" value="test"/>
        <env name="CHANGE_HEALTHCARE_SECRET" value="test"/>

        <!-- # OpenSearch -->
        <env name="OS_HOSTS" value=""/>
        <env name="OS_USERNAME" value="test"/>
        <env name="OS_PASSWORD" value="test"/>
    </php>
    <source>
        <include>
            <directory suffix=".php">./app</directory>
        </include>
    </source>
</phpunit>
