<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('regions', function (Blueprint $table) {
            $table->foreignId('manufacturer_id')->nullable()->constrained('manufacturers')->nullOnDelete();
        });

        Schema::table('districts', function (Blueprint $table) {
            $table->foreignId('manufacturer_id')->nullable()->constrained('manufacturers')->nullOnDelete();
        });

        Schema::table('territories', function (Blueprint $table) {
            $table->foreignId('manufacturer_id')->nullable()->constrained('manufacturers')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('regions', function (Blueprint $table) {
            $table->dropConstrainedForeignId('manufacturer_id');
        });

        Schema::table('districts', function (Blueprint $table) {
            $table->dropConstrainedForeignId('manufacturer_id');
        });

        Schema::table('territories', function (Blueprint $table) {
            $table->dropConstrainedForeignId('manufacturer_id');
        });
    }
};
