<?php

use App\Enums\IdentificationTokenTypeEnum;
use App\Models\IdentificationToken;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $invitations = DB::table('user_invitations')->get();

        foreach ($invitations as $invitation) {
            IdentificationToken::query()->insert([
                'identifiable_type' => User::class,
                'identifiable_id' => $invitation->user_id,
                'token_type' => IdentificationTokenTypeEnum::INVITATION->value,
                'token' => $invitation->token,
                'expires_at' => $invitation->expires_at,
                'created_at' => $invitation->created_at,
                'updated_at' => $invitation->updated_at,
            ]);
        }

        Schema::dropIfExists('user_invitations');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('user_invitations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->unique()->constrained()->cascadeOnDelete();
            $table->string('token')->unique();
            $table->dateTime('expires_at')->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate();
        });

        $tokens = IdentificationToken::query()->where('token_type', IdentificationTokenTypeEnum::INVITATION->value)->get();

        foreach ($tokens as $token) {
            DB::table('user_invitations')->insert([
                'user_id' => $token->identifiable_id,
                'token' => $token->token,
                'expires_at' => $token->expires_at,
                'created_at' => $token->created_at,
                'updated_at' => $token->updated_at,
            ]);

            $token->delete();
        }
    }
};
