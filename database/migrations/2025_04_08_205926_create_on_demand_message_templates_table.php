<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('on_demand_message_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('distributor_id')->constrained()->onDelete('cascade'); // Foreign key to distributors table
            $table->string('name'); // name (string)
            $table->text('body'); // body (text)
            $table->boolean('is_active')->default(1); // 1 = active, 0 = inactive
            $table->softDeletes(); // deleted_at (for soft delete)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('on_demand_message_templates');
    }
};
