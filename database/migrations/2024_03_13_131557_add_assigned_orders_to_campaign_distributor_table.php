<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('campaign_distributor', function (Blueprint $table) {
            $table->smallInteger('assigned_orders', unsigned: true)->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('campaign_distributor', function (Blueprint $table) {
            $table->dropColumn('assigned_orders');
        });
    }
};
