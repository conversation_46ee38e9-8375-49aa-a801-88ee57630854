<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_cms_hcpc', function (Blueprint $table) {
            $table->foreignId('product_id')->constrained('products');
            $table->foreignId('cms_hcpc_id')->constrained('cms_hcpcs');

            $table->unique(['product_id', 'cms_hcpc_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_cms_hcpc');
    }
};
