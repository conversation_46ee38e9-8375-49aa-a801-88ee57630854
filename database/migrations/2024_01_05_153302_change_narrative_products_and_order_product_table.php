<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            DB::statement('UPDATE products SET default_narrative_unit = \'change_every\' WHERE default_narrative_unit IN(\'change_infusion_set_every\',\'change_sensor_every\',\'change_transmitter_every\')');
            DB::statement('UPDATE order_product SET narrative_unit = \'change_every\' WHERE narrative_unit IN(\'change_infusion_set_every\',\'change_sensor_every\',\'change_transmitter_every\')');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
