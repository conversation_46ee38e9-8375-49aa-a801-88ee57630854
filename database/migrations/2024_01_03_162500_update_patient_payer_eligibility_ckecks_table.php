<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patient_payer_eligibility_checks', function (Blueprint $table) {
            $table->string('service')->default('changehealthcare');
            $table->json('data')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patient_payer_eligibility_checks', function (Blueprint $table) {
            $table->dropColumn(['service', 'data']);
        });
    }
};
