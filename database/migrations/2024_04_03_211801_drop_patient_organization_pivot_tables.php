<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('distributor_patient');
        Schema::dropIfExists('manufacturer_patient');
        Schema::dropIfExists('patient_provider');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('distributor_patient', function (Blueprint $table) {
            $table->foreignId('distributor_id')->constrained()->cascadeOnDelete();
            $table->foreignId('patient_id')->constrained()->cascadeOnDelete();
            $table->string('distributor_patient_id')->nullable();
            $table->softDeletes();

            $table->index('distributor_id', 'distributor_patient_distributor_id_index');
            $table->index('patient_id', 'distributor_patient_patient_id_index');
        });
        Schema::create('manufacturer_patient', function (Blueprint $table) {
            $table->foreignId('manufacturer_id')->constrained()->cascadeOnDelete();
            $table->foreignId('patient_id')->constrained()->cascadeOnDelete();
            $table->string('manufacturer_patient_id')->nullable();
            $table->softDeletes();

            $table->index('manufacturer_id', 'manufacturer_patient_manufacturer_id_index');
            $table->index('patient_id', 'manufacturer_patient_patient_id_index');
        });
        Schema::create('patient_provider', function (Blueprint $table) {
            $table->foreignId('provider_id')->constrained()->cascadeOnDelete();
            $table->foreignId('patient_id')->constrained()->cascadeOnDelete();
            $table->string('medical_record_id')->nullable();
            $table->softDeletes();

            $table->index('provider_id', 'patient_provider_provider_id_index');
            $table->index('patient_id', 'patient_provider_patient_id_index');
        });
    }
};
