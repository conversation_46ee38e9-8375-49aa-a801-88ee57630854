<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            Schema::table('icd_10s', function (Blueprint $table) {
                $table->string('code', 8)->change();
            });

            Schema::dropIfExists('cms_article_icd_10');

            Schema::create('cms_article_icd_10', function (Blueprint $table) {
                $table->foreignId('cms_article_id')->constrained()->cascadeOnDelete();
                $table->foreignId('icd_10_id')->constrained()->cascadeOnDelete();

                $table->unique(['cms_article_id', 'icd_10_id']);
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::transaction(function () {
            Schema::table('icd_10s', function (Blueprint $table) {
                $table->char('code', 8)->change();
            });

            Schema::dropIfExists('cms_article_icd_10');

            Schema::create('cms_article_icd_10', function (Blueprint $table) {
                $table->foreignId('cms_article_id');
                $table->foreignId('icd_10_id');
            });
        });
    }
};
