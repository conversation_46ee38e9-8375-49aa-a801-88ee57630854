<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            // Add the preferred_communication_method column as an ENUM
            $table->enum('preferred_communication_method', [
                'sms_enabled',
                'email_enabled',
                'call_enabled',
            ])->nullable() // Default value is null if no method is specified
                ->default(null);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->dropColumn('preferred_communication_method');
        });
    }
};
