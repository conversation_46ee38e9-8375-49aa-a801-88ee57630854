<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('same_and_similar_checks', function (Blueprint $table) {
            $table->renameColumn('request_expected_time', 'response_expected_time');
            $table->dateTime('response_expected_at')->after('response_expected_time')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('same_and_similar_checks', function (Blueprint $table) {
            $table->renameColumn('response_expected_time', 'request_expected_time');
            $table->dropColumn('response_expected_at');
        });
    }
};
