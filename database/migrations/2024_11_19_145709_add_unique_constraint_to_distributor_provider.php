<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('distributor_provider', function (Blueprint $table) {
            $table->unique(['distributor_id', 'provider_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('distributor_provider', function (Blueprint $table) {
            $table->dropUnique(['distributor_id', 'provider_id']);
        });
    }
};
