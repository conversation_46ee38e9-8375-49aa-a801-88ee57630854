<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_request_icd_10', function (Blueprint $table) {
            $table->foreignId('document_request_id')->constrained()->cascadeOnDelete();
            $table->foreignId('icd_10_id')->constrained()->cascadeOnDelete();

            $table->unique(['document_request_id', 'icd_10_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_request_icd_10');
    }
};
