<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('patient_aob_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_document_request_id')->constrained();
            $table->foreignId('patient_id')->constrained();
            $table->string('token', 64);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('patient_aob_requests');
    }
};
