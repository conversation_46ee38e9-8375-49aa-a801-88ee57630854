<?php

use App\Models\Order;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            Schema::rename('order_envelopes', 'envelopes');
            Schema::table('envelopes', function (Blueprint $table) {
                $table->string('envelopeable_type')->default(Order::class);

                $table->dropIndex('order_envelopes_order_id_index');
                $table->renameColumn('order_id', 'envelopeable_id');
                $table->index(['envelopeable_id', 'envelopeable_type']);
            });

            Schema::table('envelopes', function (Blueprint $table) {
                $table->string('envelopeable_type')->default(null)->change();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::transaction(function () {
            Schema::rename('envelopes', 'order_envelopes');
            Schema::table('order_envelopes', function (Blueprint $table) {
                $table->renameColumn('envelopeable_id', 'order_id');
                $table->dropIndex('envelopes_envelopeable_id_envelopeable_type_index');
                $table->dropColumn('envelopeable_type');
                $table->index('order_id');
            });
        });
    }
};
