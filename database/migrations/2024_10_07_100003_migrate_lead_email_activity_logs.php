<?php

use App\Models\ActivityLog;
use App\Models\Lead;
use App\Models\Order;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            ActivityLog::with('activityable')
                ->where('activityable_type', Lead::class)
                ->where('type', 'email')
                ->chunkById(500, function (Collection $activityLogs) {
                    $activityLogs->each(function (ActivityLog $activityLog) {
                        $lead = $activityLog->activityable;

                        $this->updateActivityLog($activityLog, $lead);
                    });
                });

            ActivityLog::with('activityable.lead')
                ->where('activityable_type', Order::class)
                ->where('type', 'email')
                ->chunkById(500, function (Collection $activityLogs) {
                    $activityLogs->each(function (ActivityLog $activityLog) {
                        $lead = $activityLog->activityable->lead;

                        $this->updateActivityLog($activityLog, $lead);
                    });
                });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }

    private function updateActivityLog(ActivityLog $activityLog, Lead $lead): void
    {
        $metadata = $activityLog->metadata;

        if (!isset($metadata['template_name']) && isset($metadata['name'])) {
            $metadata['template_name'] = $metadata['name'];
        }

        $metadata['lead'] = [
            'first_name' => $lead->first_name,
            'last_name' => $lead->last_name,
            'email' => $lead->email,
        ];

        $activityLog->update([
            'metadata' => $metadata,
        ]);
    }
};
