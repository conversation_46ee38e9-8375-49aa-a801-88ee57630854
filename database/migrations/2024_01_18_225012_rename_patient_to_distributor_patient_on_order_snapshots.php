<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_snapshots', function (Blueprint $table) {
            $table->renameColumn('patient', 'distributor_patient');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_snapshots', function (Blueprint $table) {
            $table->renameColumn('distributor_patient', 'patient');
        });
    }
};
