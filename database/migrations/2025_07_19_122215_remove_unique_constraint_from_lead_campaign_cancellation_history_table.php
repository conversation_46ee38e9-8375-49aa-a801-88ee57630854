<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lead_campaign_cancellation_history', function (Blueprint $table) {
            $table->dropUnique('lead_campaign_cancellation_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lead_campaign_cancellation_history', function (Blueprint $table) {
            $table->unique(['lead_id', 'distributor_campaign_id', 'canceled_date'], 'lead_campaign_cancellation_unique');
        });
    }
};
