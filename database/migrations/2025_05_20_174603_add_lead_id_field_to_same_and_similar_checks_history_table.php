<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('same_and_similar_checks_history', function (Blueprint $table) {
            $table->bigInteger('lead_id')->nullable()->after('order_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('same_and_similar_checks_history', function (Blueprint $table) {
            $table->dropColumn('lead_id');
        });
    }
};
