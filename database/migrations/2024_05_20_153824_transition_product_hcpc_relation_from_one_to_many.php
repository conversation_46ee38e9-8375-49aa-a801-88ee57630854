<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            DB::transaction(function () use ($table) {
                DB::table('products')->chunkById(100, function (Collection $products) {
                    $products->each(function ($product) {
                        $cmsHcpc = DB::table('cms_hcpcs')->find($product->cms_hcpc_id);

                        if ($cmsHcpc) {
                            DB::table('product_cms_hcpc')->insert([
                                'product_id' => $product->id,
                                'cms_hcpc_id' => $cmsHcpc->id,
                            ]);
                        }

                        $commercialHcpc = DB::table('cms_hcpcs')->find($product->commercial_hcpc_id);

                        if ($commercialHcpc) {
                            DB::table('product_commercial_hcpc')->insert([
                                'product_id' => $product->id,
                                'cms_hcpc_id' => $commercialHcpc->id,
                            ]);
                        }
                    });
                });

                $table->dropColumn('cms_hcpc_id');
                $table->dropColumn('commercial_hcpc_id');
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::transaction(function () {
            Schema::table('products', function (Blueprint $table) {
                $table->foreignId('cms_hcpc_id')->nullable();
                $table->foreignId('commercial_hcpc_id')->nullable();
            });

            DB::table('products')->chunkById(100, function (Collection $products) {
                $products->each(function ($product) {
                    $cmsHcpc = DB::table('product_cms_hcpc')
                        ->where('product_id', $product->id)
                        ->first();

                    if ($cmsHcpc) {
                        DB::table('products')
                            ->where('id', $product->id)
                            ->update(['cms_hcpc_id' => $cmsHcpc->cms_hcpc_id]);
                    }

                    $commercialHcpc = DB::table('product_commercial_hcpc')
                        ->where('product_id', $product->id)
                        ->first();

                    if ($commercialHcpc) {
                        DB::table('products')
                            ->where('id', $product->id)
                            ->update(['commercial_hcpc_id' => $commercialHcpc->cms_hcpc_id]);
                    }
                });
            });
        });
    }
};
