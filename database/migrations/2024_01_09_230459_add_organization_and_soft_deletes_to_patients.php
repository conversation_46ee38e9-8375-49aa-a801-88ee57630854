<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->foreignId('global_patient_id')->nullable(true)->constrained('global_patients');
            $table->string('organization_type', 30)->nullable(true);
            $table->bigInteger('organization_id')->nullable(true);
            $table->string('external_id')->nullable(true);
            $table->softDeletes();

            $table->unique(['global_patient_id', 'organization_type', 'organization_id'], 'patients_global_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->dropUnique('patients_global_unique');

            $table->dropConstrainedForeignId('global_patient_id');
            $table->dropColumn('organization_type');
            $table->dropColumn('organization_id');
            $table->dropColumn('external_id');
            $table->dropSoftDeletes();
        });
    }
};
