<?php

use App\Enums\EligibilityStatusEnum;
use App\Enums\PatientPayerStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            Schema::table('patient_payer', function (Blueprint $table) {
                $table->string('manual_status')->default(EligibilityStatusEnum::ACTIVE->value);
                $table->string('status')->default(PatientPayerStatusEnum::ACTIVE->value)->change();
            });

            DB::statement('UPDATE patient_payer SET status = \'' . PatientPayerStatusEnum::ACTIVE->value . '\' WHERE status = \'unable_to_check\'');
            DB::statement('UPDATE patient_payer SET status = \'' . PatientPayerStatusEnum::ACTIVE->value . '\' WHERE status = \'unchecked\'');

            Schema::table('patient_payer_eligibility_checks', function (Blueprint $table) {
                $table->string('status')->default('unchecked');
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patient_payer', function (Blueprint $table) {
            $table->dropColumn('manual_status');
        });

        Schema::table('patient_payer_eligibility_checks', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
