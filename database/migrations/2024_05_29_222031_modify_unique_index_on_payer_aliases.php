<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payer_aliases', function (Blueprint $table) {
            $table->dropUnique(['payer_id', 'alias']);
            $table->unique('alias');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payer_aliases', function (Blueprint $table) {
            $table->dropUnique(['alias']);
            $table->unique(['payer_id', 'alias']);
        });
    }
};
