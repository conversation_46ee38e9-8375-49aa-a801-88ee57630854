<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patient_payer_eligibility_checks', function (Blueprint $table) {
            $table->json('errors')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patient_payer_eligibility_checks', function (Blueprint $table) {
            $table->dropColumn('errors');
        });
    }
};
