<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cms_hcpc_ancillary_cms_hcpc', function (Blueprint $table) {
            $table->foreignId('cms_hcpc_id')->constrained();
            $table->foreignId('ancillary_cms_hcpc_id')->constrained('cms_hcpcs');

            $table->index('cms_hcpc_id');
            $table->index('ancillary_cms_hcpc_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cms_hcpc_ancillary_cms_hcpc');
    }
};
