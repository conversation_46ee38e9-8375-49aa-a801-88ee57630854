<?php

use App\Enums\UserPermissionEnum;
use Illuminate\Database\Migrations\Migration;
use Spatie\Permission\Models\Permission;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ensure the model is available and use updateOrCreate
        $managePhysicianPermission = Permission::where(['name' => UserPermissionEnum::MANAGE_PHYSICIANS->value, 'guard_name' => config('auth.defaults.guard')])->first();

        if (!$managePhysicianPermission) {
            Permission::create(
                ['name' => UserPermissionEnum::MANAGE_PHYSICIANS->value, 'guard_name' => config('auth.defaults.guard')],
            );
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Permission::where('name', UserPermissionEnum::MANAGE_PHYSICIANS->value)
            ->where('guard_name', config('auth.defaults.guard'))
            ->delete();
    }
};
