<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cms_hcpc_medical_policy_form', function (Blueprint $table) {
            $table->primary(['medical_policy_form_id', 'cms_hcpc_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cms_hcpc_medical_policy_form', function (Blueprint $table) {
            $table->dropPrimary(['medical_policy_form_id', 'cms_hcpc_id']);
        });
    }
};
