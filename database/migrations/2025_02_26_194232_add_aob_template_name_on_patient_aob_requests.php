<?php

use App\Models\Distributor;
use App\Models\PatientAobRequest;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            Schema::table('patient_aob_requests', function (Blueprint $table) {
                $table->string('aob_template_name')->nullable();
            });

            $distributors = Distributor::query()
                ->select(['id', 'aob_template_name'])
                ->get()
                ->keyBy('id');

            PatientAobRequest::query()
                ->with('patient')
                ->chunk(100, function (Collection $patientAobRequests) use ($distributors) {
                    $patientAobRequests->each(function (PatientAobRequest $patientAobRequest) use ($distributors) {
                        $distributor = $distributors->get($patientAobRequest->patient->organization_id);

                        $newAobTemplateName = match ($distributor->aob_template_name) {
                            'aob_appy' => 'aob_appy_en',
                            'aob_ehcs' => 'aob_ehcs_en',
                            null => 'aob_appy_en',
                        };

                        $patientAobRequest->update([
                            'aob_template_name' => $newAobTemplateName,
                        ]);
                    });
                });

            Schema::table('patient_aob_requests', function (Blueprint $table) {
                $table->string('aob_template_name')->nullable(false)->change();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patient_aob_requests', function (Blueprint $table) {
            $table->dropColumn('aob_template_name');
        });
    }
};
