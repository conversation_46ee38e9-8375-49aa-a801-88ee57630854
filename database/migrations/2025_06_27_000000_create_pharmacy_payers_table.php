<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pharmacy_payers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('payer_id')->constrained('payers'); // FK to payers table
            $table->string('policy_number');
            $table->string('group_number')->nullable();
            $table->text('notes')->nullable();
            $table->morphs('payable'); // payable_id, payable_type
            $table->softDeletes(); // For soft delete functionality
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pharmacy_payers');
    }
};
