<?php

use App\Enums\PatientProviderUserAssociationType;
use App\Extensions\Logger;
use App\Models\Distributor;
use App\Models\Order;
use App\Models\PatientProviderUser;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $distributors = Distributor::query()->with([
            'users' => function ($query) {
                $query->where('owner', true);
            },
        ])
            ->get()
            ->keyBy('id');

        DB::transaction(function () use ($distributors) {
            DB::table('patients')
                ->where('organization_type', Distributor::class)
                ->chunkById(500, function (Collection $patients) use ($distributors) {
                    $patients->each(function (stdClass $patient) use ($distributors) {
                        $distributor = $distributors->get($patient->organization_id);
                        $accountOwner = $distributor->users->first();// Owner

                        $orderProviderUserId = Order::query()
                            ->whereHas('providerUser', function ($query) {
                                $query->whereHas('npiRecord');
                            })
                            ->where('global_patient_id', $patient->global_patient_id)
                            ->where('distributor_id', $patient->organization_id)
                            ->where('created_at', '<', Carbon::now()->subDays(90))
                            ->orderByDesc('created_at')
                            ->limit(1)
                            ->pluck('provider_user_id')
                            ->first();

                        if ($orderProviderUserId) {
                            PatientProviderUser::create([
                                'patient_id' => $patient->id,
                                'provider_user_id' => $orderProviderUserId,
                                'updated_by' => $accountOwner->id,
                                'association_type' => PatientProviderUserAssociationType::ORDERING,
                                'distributor_id' => $distributor->id,
                            ]);
                        } else {
                            Logger::debug("No ordering physician found for patient id: {$patient->id}, distributor id: {$distributor->id}.");
                        }
                    });
                });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('patient_provider_users')->truncate();
    }
};
