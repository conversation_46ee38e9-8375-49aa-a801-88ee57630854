<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_tasks', function (Blueprint $table) {
            // Add request_id as nullable foreign key to document_requests table
            $table->unsignedBigInteger('request_id')->nullable()->after('order_id');

            // Add is_automated boolean column with default false
            $table->boolean('is_automated')->default(false)->after('metadata');

            // Add foreign key constraint
            $table->foreign('request_id')->references('id')->on('document_requests')->onDelete('set null');

            // Add indexes for performance
            $table->index('request_id');
            $table->index('is_automated');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_tasks', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['request_id']);

            // Drop indexes
            $table->dropIndex(['request_id']);
            $table->dropIndex(['is_automated']);

            // Drop columns
            $table->dropColumn(['request_id', 'is_automated']);
        });
    }
};
