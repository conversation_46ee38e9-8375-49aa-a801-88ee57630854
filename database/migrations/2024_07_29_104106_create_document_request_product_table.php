<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_request_product', function (Blueprint $table) {
            $table->foreignId('document_request_id')->constrained()->cascadeOnDelete();
            $table->foreignId('product_id')->constrained()->cascadeOnDelete();
            $table->decimal('measure_count', 12, 6)->unsigned()->default(0);
            $table->string('measure_unit')->nullable();
            $table->unsignedInteger('duration_count')->default(0);
            $table->string('duration_unit')->nullable();
            $table->string('narrative_unit')->nullable();
            $table->string('narrative_measure_unit')->nullable();
            $table->decimal('narrative_measure_count', 12, 6)->unsigned()->default(0);
            $table->string('serial_number')->nullable();
            $table->timestamps();

            $table->index('document_request_id', 'document_request_product_document_request_id_index');
            $table->index('product_id', 'document_request_product_product_id_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_request_product');
    }
};
