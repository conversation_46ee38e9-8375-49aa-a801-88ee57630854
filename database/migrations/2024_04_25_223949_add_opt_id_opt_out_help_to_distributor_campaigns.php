<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('distributor_campaigns', function (Blueprint $table) {
            $table->text('opt_in_text')->nullable();
            $table->text('opt_out_text')->nullable();
            $table->text('help_text')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('distributor_campaigns', function (Blueprint $table) {
            $table->dropColumn('opt_in_text');
            $table->dropColumn('opt_out_text');
            $table->dropColumn('help_text');
        });
    }
};
