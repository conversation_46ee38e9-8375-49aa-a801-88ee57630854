<?php

use App\Enums\ValueTypeEnum;
use App\Models\Distributor;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Distributor::with('organizationSettings')->chunk(100, function ($distributors) {
            foreach ($distributors as $distributor) {
                $distributor->organizationSettings()->updateOrCreate([
                    'name' => 'website_url',
                ], [
                    'value' => '',
                    'value_type' => ValueTypeEnum::STRING,
                ]);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Distributor::with('organizationSettings')->chunk(100, function ($distributors) {
            foreach ($distributors as $distributor) {
                $distributor->organizationSettings()->where('name', 'website_url')->delete();
            }
        });
    }
};
