<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cms_hcpcs', function (Blueprint $table) {
            $table->smallInteger('length_of_need')->nullable();
            $table->string('length_of_need_units')->nullable();
            $table->smallInteger('quantity')->nullable();
            $table->string('quantity_units')->nullable();
            $table->string('narrative')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cms_hcpcs', function (Blueprint $table) {
            $table->dropColumn('length_of_need');
            $table->dropColumn('length_of_need_units');
            $table->dropColumn('quantity');
            $table->dropColumn('quantity_units');
            $table->dropColumn('narrative');
        });
    }
};
