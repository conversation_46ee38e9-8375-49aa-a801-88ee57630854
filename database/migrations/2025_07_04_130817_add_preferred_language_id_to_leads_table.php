<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->unsignedBigInteger('preferred_language_id')->nullable();
            $table->foreign('preferred_language_id')
                ->references('id')->on('languages')
                ->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->dropForeign(['preferred_language_id']);
            $table->dropColumn('preferred_language_id');
        });
    }
};
