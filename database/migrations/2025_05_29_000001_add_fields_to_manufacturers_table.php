<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('manufacturers', function (Blueprint $table) {
            $table->boolean('enable_shipment_update_fax')->default(false);
            $table->boolean('appy_medical_specific_instance')->default(false);
            $table->boolean('enable_phi_visibility')->default(false);
            $table->json('allowed_distributor_ids')->nullable(); // for multi-select dropdown
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('manufacturers', function (Blueprint $table) {
            $table->dropColumn([
                'enable_shipment_update_fax',
                'appy_medical_specific_instance',
                'enable_phi_visibility',
                'allowed_distributor_ids',
            ]);
        });
    }
};
