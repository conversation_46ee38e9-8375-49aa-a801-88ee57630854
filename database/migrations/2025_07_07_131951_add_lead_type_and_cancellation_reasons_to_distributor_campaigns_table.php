<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('distributor_campaigns', function (Blueprint $table) {
            $table->json('lead_type')->nullable();
            $table->json('cancellation_reasons')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('distributor_campaigns', function (Blueprint $table) {
            $table->dropColumn(['lead_type', 'cancellation_reasons']);
        });
    }
};
