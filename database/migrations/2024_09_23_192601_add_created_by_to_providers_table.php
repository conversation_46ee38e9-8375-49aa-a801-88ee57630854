<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('providers', function (Blueprint $table) {
            $table->foreignId('created_by')->nullable()->constrained('users');
        });

        $admin = DB::table('users')->whereNull('entity_user_type')->oldest()->first();

        if ($admin) {
            DB::table('providers')->update(['created_by' => $admin->id]);
        }

        Schema::table('providers', function (Blueprint $table) {
            $table->bigInteger('created_by')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('providers', function (Blueprint $table) {
            $table->dropColumn('created_by');
        });
    }
};
