<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('zip_codes', function (Blueprint $table) {
            $table->dropColumn(['area_id', 'area_type']);
        });

        Schema::create('area_zip_code', function (Blueprint $table) {
            $table->id();
            $table->foreignId('zip_code_id')->constrained('zip_codes')->onDelete('cascade');
            $table->morphs('area');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('area_zip_code');

        Schema::table('zip_codes', function (Blueprint $table) {
            $table->unsignedBigInteger('area_id');
            $table->string('area_type');
        });
    }
};
