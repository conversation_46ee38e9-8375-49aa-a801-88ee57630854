<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasColumn('same_and_similar_checks', 'request_expected_time')) {
            Schema::table('same_and_similar_checks', function (Blueprint $table) {
                $table->renameColumn('request_expected_time', 'response_expected_time');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('same_and_similar_checks', 'response_expected_time')) {
            Schema::table('same_and_similar_checks', function (Blueprint $table) {
                $table->renameColumn('response_expected_time', 'request_expected_time');
            });
        }
    }
};
