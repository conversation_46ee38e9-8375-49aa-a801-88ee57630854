<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('faxes', function (Blueprint $table) {
            $table->foreignId('removed_by')->nullable()->constrained('users');
            $table->text('removed_note')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('faxes', function (Blueprint $table) {
            $table->dropForeign(['removed_by']);
            $table->dropColumn(['removed_by', 'removed_note']);
        });
    }
};
