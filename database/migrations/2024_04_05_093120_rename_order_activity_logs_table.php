<?php

use App\Models\Order;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            Schema::rename('order_activity_logs', 'activity_logs');
            Schema::table('activity_logs', function (Blueprint $table) {
                $table->string('activityable_type')->default(Order::class);
                $table->dropForeign('order_activity_logs_order_id_foreign');
                $table->dropIndex('order_activity_logs_order_id_type_index');
                $table->renameColumn('order_id', 'activityable_id');
                $table->renameIndex('order_activity_logs_user_id_type_index', 'activity_logs_user_id_type_index');
                $table->renameIndex('order_activity_logs_pkey', 'activity_logs_pkey');
                $table->dropForeign('order_activity_logs_user_id_foreign');
                $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();
                $table->index(['activityable_type', 'activityable_id'], 'activity_logs_activityable_id_type_index');
            });

            Schema::table('activity_logs', function (Blueprint $table) {
                $table->string('activityable_type')->default(null)->change();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::rename('activity_logs', 'order_activity_logs');
        Schema::table('order_activity_logs', function (Blueprint $table) {
            $table->dropForeign('activity_logs_user_id_foreign');
            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();
            $table->dropIndex('activityable_id_type_index');
            $table->dropColumn('activityable_type');
            $table->renameIndex('activity_logs_user_id_type_index', 'order_activity_logs_user_id_type_index');
            $table->renameIndex('activity_logs_pkey', 'order_activity_logs_pkey');
            $table->renameColumn('activityable_id', 'order_id');
            $table->foreign('order_id')->references('id')->on('orders')->cascadeOnDelete();
            $table->index('order_id', 'order_activity_logs_order_id_type_index');
        });
    }
};
