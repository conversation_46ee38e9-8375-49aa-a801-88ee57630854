<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('manufacturers', function (Blueprint $table) {
            $table->string('organization_url')->nullable();
            $table->string('assigned_fax')->nullable();
            $table->string('bt_user')->nullable();
            $table->string('bt_pass')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('manufacturers', function (Blueprint $table) {
            $table->dropColumn(['organization_url', 'assigned_fax', 'brightree_email', 'brightree_password']);
        });
    }
};
