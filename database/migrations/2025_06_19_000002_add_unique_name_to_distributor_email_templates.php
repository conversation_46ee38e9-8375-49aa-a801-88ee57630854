<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add a partial unique index for name where deleted_at is null
        DB::statement('CREATE UNIQUE INDEX distributor_email_templates_name_unique ON distributor_email_templates (name) WHERE deleted_at IS NULL');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('distributor_email_templates', function (Blueprint $table) {
            // Drop the partial unique index
            DB::statement('DROP INDEX distributor_email_templates_name_unique');
        });
    }
};
