<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('eligibility_checks', function (Blueprint $table) {
            $table->text('eligibility_response_edi')->nullable();
            $table->json('eligibility_response_parsed')->nullable();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('eligibility_checks', function (Blueprint $table) {
            $table->dropColumn('eligibility_response_edi');
            $table->dropColumn('eligibility_response_parsed');
        });
    }
};
