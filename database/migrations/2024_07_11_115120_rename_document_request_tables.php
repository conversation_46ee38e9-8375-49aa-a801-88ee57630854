<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            Schema::rename('order_document_requests', 'document_requests');

            Schema::table('order_documents', function (Blueprint $table) {
                $table->dropForeign('order_documents_order_document_request_id_foreign');
                $table->renameColumn('order_document_request_id', 'document_request_id');
                $table->foreign('document_request_id')->references('id')->on('document_requests')->cascadeOnDelete();
            });

            Schema::rename('order_document_request_history', 'document_request_history');
            Schema::table('document_request_history', function (Blueprint $table) {
                $table->dropForeign('order_document_request_history_order_document_request_id_foreign');
                $table->renameColumn('order_document_request_id', 'document_request_id');
                $table->foreign('document_request_id')->references('id')->on('document_requests')->cascadeOnDelete();
            });

            Schema::table('patient_aob_requests', function (Blueprint $table) {
                $table->dropForeign('patient_aob_requests_order_document_request_id_foreign');
                $table->renameColumn('order_document_request_id', 'document_request_id');
                $table->foreign('document_request_id')->references('id')->on('document_requests');
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::transaction(function () {
            Schema::rename('document_requests', 'order_document_requests');

            Schema::table('order_documents', function (Blueprint $table) {
                $table->dropForeign('order_documents_document_request_id_foreign');
                $table->renameColumn('document_request_id', 'order_document_request_id');
                $table->foreign('order_document_request_id')->references('id')->on('order_document_requests')->cascadeOnDelete();
            });

            Schema::rename('document_request_history', 'order_document_request_history');
            Schema::table('order_document_request_history', function (Blueprint $table) {
                $table->dropForeign('document_request_history_document_request_id_foreign');
                $table->renameColumn('document_request_id', 'order_document_request_id');
                $table->foreign('order_document_request_id')->references('id')->on('order_document_requests')->cascadeOnDelete();
            });

            Schema::table('patient_aob_requests', function (Blueprint $table) {
                $table->dropForeign('patient_aob_requests_document_request_id_foreign');
                $table->renameColumn('document_request_id', 'order_document_request_id');
                $table->foreign('order_document_request_id')->references('id')->on('order_document_requests');
            });
        });
    }
};
