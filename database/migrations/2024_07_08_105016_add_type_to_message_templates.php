<?php

use App\Enums\MessageTemplateTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('message_templates', function (Blueprint $table) {
            $table->string('type')->default(MessageTemplateTypeEnum::DIGITAL_JOURNEY->value);
        });

        Schema::table('message_templates', function (Blueprint $table) {
            $table->string('type')->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('message_templates', function (Blueprint $table) {
            $table->dropColumn('type');
        });
    }
};
