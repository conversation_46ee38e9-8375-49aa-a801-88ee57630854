<?php

use App\Enums\DistributorUserTypeEnum;
use App\Extensions\Logger;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Spatie\Permission\Exceptions\RoleDoesNotExist;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        try {
            User::role(DistributorUserTypeEnum::EXTERNAL_USER)->withTrashed()->update(['notifications' => true]);
        } catch (RoleDoesNotExist $e) {
            // ignore
            Logger::warning('Role does not exist. It is ok to skip when unit tests are running.');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // not possible to reverse
    }
};
