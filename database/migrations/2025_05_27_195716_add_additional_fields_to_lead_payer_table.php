<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lead_payer', function (Blueprint $table) {
            $table->integer('deductible')->nullable();
            $table->integer('co_insurance')->nullable();
            $table->integer('max_oop')->nullable();
            $table->text('comment')->nullable();
            $table->string('manual_status')->nullable();
            $table->integer('in_deductible')->nullable();
            $table->integer('in_co_insurance')->nullable();
            $table->integer('in_max_oop')->nullable();
            $table->integer('deductible_remaining')->nullable();
            $table->integer('in_deductible_remaining')->nullable();
            $table->integer('max_oop_remaining')->nullable();
            $table->integer('in_max_oop_remaining')->nullable();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lead_payer', function (Blueprint $table) {
            $table->dropColumn([
                'deductible',
                'co_insurance',
                'max_oop',
                'comment',
                'manual_status',
                'in_deductible',
                'in_co_insurance',
                'in_max_oop',
                'deductible_remaining',
                'in_deductible_remaining',
                'max_oop_remaining',
                'in_max_oop_remaining',
            ]);
        });
    }
};
