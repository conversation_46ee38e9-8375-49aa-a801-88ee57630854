<?php

use App\Enums\OrderDocumentSourceEnum;
use App\Enums\OrderDocumentTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_documents', function (Blueprint $table) {
            $table->string('source', 30)->default(OrderDocumentSourceEnum::SYSTEM->value);
        });

        $mpfSource = OrderDocumentSourceEnum::MPF->value;
        $mpfType = OrderDocumentTypeEnum::MPF->value;

        DB::statement("UPDATE order_documents SET source = '{$mpfSource}' WHERE type = '{$mpfType}'");

        $distributorUserSource = OrderDocumentSourceEnum::DISTRIBUTOR_USER->value;
        $distributorUserTypes = implode(
            '\',\'',
            [
                OrderDocumentTypeEnum::CHART_NOTES->value,
                OrderDocumentTypeEnum::LAB->value,
                OrderDocumentTypeEnum::APPOINTMENT_CONFIRMATION->value,
                OrderDocumentTypeEnum::OTHER->value,
                OrderDocumentTypeEnum::CUSTOM->value,
                OrderDocumentTypeEnum::CMN->value,
            ],
        );

        DB::statement("UPDATE order_documents SET source = '{$distributorUserSource}' WHERE type IN ('{$distributorUserTypes}')");

        $systemSource = OrderDocumentSourceEnum::SYSTEM->value;
        $cmnType = OrderDocumentTypeEnum::CMN->value;

        DB::statement("UPDATE order_documents SET source = '{$systemSource}' WHERE type = '{$cmnType}' AND uploaded_by IS NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_documents', function (Blueprint $table) {
            $table->dropColumn('source');
        });
    }
};
