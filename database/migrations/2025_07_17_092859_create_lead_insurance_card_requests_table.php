<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lead_insurance_card_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lead_id')->constrained()->onDelete('cascade');
            $table->foreignId('front_image_file_id')->nullable()->constrained('files')->onDelete('set null');
            $table->foreignId('back_image_file_id')->nullable()->constrained('files')->onDelete('set null');
            $table->string('request_token', 64)->unique();
            $table->string('status', 20)->default('pending');
            $table->text('notes')->nullable();
            $table->timestamp('expiration_date');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['lead_id', 'status']);
            $table->index(['status', 'expiration_date']);
            $table->index(['request_token']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lead_insurance_card_requests');
    }
};
