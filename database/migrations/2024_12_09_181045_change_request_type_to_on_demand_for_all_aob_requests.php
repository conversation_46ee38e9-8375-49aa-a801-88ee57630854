<?php

use App\Enums\DocumentRequestRequestTypeEnum;
use App\Enums\DocumentRequestTypeEnum;
use App\Enums\LogGroupEnum;
use App\Extensions\Logger;
use App\Models\DocumentRequest;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $requestsQuery = DocumentRequest::query()
            ->where('type', DocumentRequestTypeEnum::AOB)
            ->where('request_type', '!=', DocumentRequestRequestTypeEnum::ON_DEMAND);

        $requestIds = $requestsQuery->pluck('id');

        $requestsQuery->update(['request_type' => DocumentRequestRequestTypeEnum::ON_DEMAND]);

        Logger::info(
            'Changed request type to on demand for all AOB requests',
            LogGroupEnum::DEFAULT,
            [
                'request_ids' => $requestIds->implode(', '),
            ],
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
