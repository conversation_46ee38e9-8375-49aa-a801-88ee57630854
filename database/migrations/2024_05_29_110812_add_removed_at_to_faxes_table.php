<?php

use App\Enums\FaxReviewStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            Schema::table('faxes', function (Blueprint $table) {
                $table->dateTime('removed_at')->nullable();
            });

            DB::statement('UPDATE faxes SET removed_at = \'' . Carbon::now()->toDateTimeString() . '\', review_status = \'' . FaxReviewStatusEnum::UNIDENTIFIED->value . '\' WHERE review_status = \'deleted\'');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::transaction(function () {
            DB::statement('UPDATE faxes SET review_status = \'deleted\' WHERE removed_at IS NOT NULL');

            Schema::table('faxes', function (Blueprint $table) {
                $table->removeColumn('removed_at');
            });
        });
    }
};
