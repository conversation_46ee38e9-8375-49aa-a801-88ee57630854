<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payer_services', function (Blueprint $table) {
            $table->string('eligibility_id', 20)->nullable();
            $table->string('ch_service_id', 20)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payer_services', function (Blueprint $table) {
            $table->dropColumn('eligibility_id');
            $table->char('ch_service_id', 15)->change();
        });
    }
};
