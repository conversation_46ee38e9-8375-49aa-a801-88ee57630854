<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Artisan;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Run the MessageConfigurationSeeder
        Artisan::call('db:seed', [
            '--class' => 'Database\\Seeders\\MessageConfigurationSeeder',
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
