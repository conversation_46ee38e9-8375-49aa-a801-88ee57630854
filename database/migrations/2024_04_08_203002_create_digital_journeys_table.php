<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('digital_journeys', function (Blueprint $table) {
            $table->id();
            $table->string('communication_method', 30);
            $table->unsignedTinyInteger('days_from_created_date');
            $table->foreignId('distributor_campaign_id')->constrained('distributor_campaigns')->cascadeOnDelete();
            $table->foreignId('message_template_id')->constrained('message_templates')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('digital_journeys');
    }
};
