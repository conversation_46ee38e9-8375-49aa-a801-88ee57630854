<?php

use App\Models\Distributor;
use App\Models\District;
use App\Models\Region;
use App\Models\Territory;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        $regions = Region::where('organization_type', Distributor::class)->get();
        $districts = District::where('organization_type', Distributor::class)->get();
        $territories = Territory::where('organization_type', Distributor::class)->get();

        foreach ($regions as $region) {
            $regionManagerId = $region->manager_id;

            if ($regionManagerId) {
                $region->distributorManagers()->attach([$regionManagerId]);
            }
        }

        foreach ($districts as $district) {
            $districtManagerId = $district->manager_id;

            if ($districtManagerId) {
                $district->distributorManagers()->attach([$districtManagerId]);
            }
        }

        foreach ($territories as $territory) {
            $territoryManagerId = $territory->manager_id;

            if ($territoryManagerId) {
                $territory->distributorManagers()->attach([$territoryManagerId]);
            }
        }

    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
