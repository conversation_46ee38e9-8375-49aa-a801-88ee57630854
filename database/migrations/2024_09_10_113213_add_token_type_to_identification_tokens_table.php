<?php

use App\Enums\IdentificationTokenTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('identification_tokens', function (Blueprint $table) {
            $table->string('token_type')->default(IdentificationTokenTypeEnum::UNSUBSCRIBE_EMAIL->value);
            $table->timestamp('created_at')->useCurrent()->change();
            $table->timestamp('updated_at')->useCurrent()->useCurrentOnUpdate()->change();
        });

        Schema::table('identification_tokens', function (Blueprint $table) {
            $table->string('token_type')->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('identification_tokens', function (Blueprint $table) {
            $table->dropColumn('token_type');
        });
    }
};
