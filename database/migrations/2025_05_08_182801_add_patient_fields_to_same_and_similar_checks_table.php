<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('same_and_similar_checks', function (Blueprint $table) {
            $table->bigInteger('patient_id')->nullable();
            $table->string('patient_first_name')->nullable();
            $table->string('patient_last_name')->nullable();
            $table->string('patient_dob')->nullable();
            $table->string('patient_state_code')->nullable();
            $table->string('patient_gender')->nullable();
            $table->bigInteger('created_by')->nullable();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('same_and_similar_checks', function (Blueprint $table) {
            $table->dropColumn('patient_id');
            $table->dropColumn('patient_first_name');
            $table->dropColumn('patient_last_name');
            $table->dropColumn('patient_dob');
            $table->dropColumn('patient_state_code');
            $table->dropColumn('patient_gender');
            $table->dropColumn('created_by');

        });
    }
};
