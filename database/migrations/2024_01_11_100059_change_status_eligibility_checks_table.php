<?php

use App\Enums\EligibilityStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patient_payer_eligibility_checks', function (Blueprint $table) {
            $table->string('status')->default(EligibilityStatusEnum::UNABLE_TO_CHECK->value)->change();
        });

        DB::statement('UPDATE patient_payer_eligibility_checks SET status = \'' . EligibilityStatusEnum::UNABLE_TO_CHECK->value . '\' WHERE status = \'unchecked\'');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
