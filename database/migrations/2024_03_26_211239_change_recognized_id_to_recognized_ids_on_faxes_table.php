<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('faxes', function (Blueprint $table) {
            $table->dropColumn('recognized_id');
            $table->json('recognized_ids')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('faxes', function (Blueprint $table) {
            $table->string('recognized_id')->nullable(true);
            $table->dropColumn('recognized_ids');
        });
    }
};
