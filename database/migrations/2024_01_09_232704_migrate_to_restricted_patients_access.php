<?php

use App\Models\Distributor;
use App\Models\GlobalPatient;
use App\Models\Manufacturer;
use App\Models\Patient;
use App\Models\Provider;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Ramsey\Uuid\Uuid;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            Patient::query()->whereNull('organization_id')->chunkById(100, function (Collection $patients) {
                $patients->each(function (Patient $patient) {
                    $distributors = Distributor::query()
                        ->join('distributor_patient', 'distributor_patient.distributor_id', '=', 'distributors.id')
                        ->where('distributor_patient.patient_id', $patient->id)
                        ->select('distributors.*', 'distributor_patient.distributor_patient_id', 'distributor_patient.deleted_at')
                        ->get()
                        ->keyBy('id');

                    $globalPatient = GlobalPatient::create(['uuid' => Uuid::uuid4()]);
                    $patient->globalPatient()->associate($globalPatient);
                    $patient->save();
                    $isLinked = false;

                    $distributors->each(function (Distributor $distributor) use ($patient, &$isLinked) {
                        if ($isLinked === true) {
                            $distributorPatient = $patient->cloneForOrganization($distributor);
                        } else {
                            $distributorPatient = $patient;
                            $distributorPatient->setOrganization($distributor);
                            $distributorPatient->save();

                            $isLinked = true;
                        }

                        // store external id
                        if (!empty($distributor->distributor_patient_id)) {
                            $distributorPatient->external_id = $distributor->distributor_patient_id;
                            $distributorPatient->save();
                        }

                        // copy soft delete
                        if (!empty($distributor->deleted_at)) {
                            $distributorPatient->deleted_at = $distributor->deleted_at;
                            $distributorPatient->save();
                        }
                    });

                    $providers = Provider::query()
                        ->join('patient_provider', 'patient_provider.provider_id', '=', 'providers.id')
                        ->where('patient_provider.patient_id', $patient->id)
                        ->select('providers.*', 'patient_provider.medical_record_id', 'patient_provider.deleted_at')
                        ->get()
                        ->keyBy('id');

                    $providers->each(function (Provider $provider) use ($patient, &$isLinked) {
                        if ($isLinked === true) {
                            $providerPatient = $patient->cloneForOrganization($provider);
                        } else {
                            $providerPatient = $patient;
                            $providerPatient->setOrganization($provider);
                            $providerPatient->save();

                            $isLinked = true;
                        }

                        // store external id
                        if (!empty($provider->medical_record_id)) {
                            $providerPatient->external_id = $provider->medical_record_id;
                            $providerPatient->save();
                        }

                        // copy soft delete
                        if (!empty($provider->deleted_at)) {
                            $providerPatient->deleted_at = $provider->deleted_at;
                            $providerPatient->save();
                        }
                    });

                    // Link only thouse patients to manufacturers that are not linked to other organizations
                    if ($isLinked) {
                        return true;
                    }

                    $manufacturers = Manufacturer::query()
                        ->join('manufacturer_patient', 'manufacturer_patient.manufacturer_id', '=', 'manufacturers.id')
                        ->where('manufacturer_patient.patient_id', $patient->id)
                        ->select('manufacturers.*', 'manufacturer_patient.manufacturer_patient_id', 'manufacturer_patient.deleted_at')
                        ->get()
                        ->keyBy('id');

                    $manufacturers->each(function (Manufacturer $manufacturer) use ($patient, &$isLinked) {
                        if ($isLinked === true) {
                            $manufacturerPatient = $patient->cloneForOrganization($manufacturer);
                        } else {
                            $manufacturerPatient = $patient;
                            $manufacturerPatient->setOrganization($manufacturer);
                            $manufacturerPatient->save();

                            $isLinked = true;
                        }

                        // store external id
                        if (!empty($manufacturer->manufacturer_patient_id)) {
                            $manufacturerPatient->external_id = $manufacturer->manufacturer_patient_id;
                            $manufacturerPatient->save();
                        }

                        // copy soft delete
                        if (!empty($manufacturer->deleted_at)) {
                            $manufacturerPatient->deleted_at = $manufacturer->deleted_at;
                            $manufacturerPatient->save();
                        }
                    });
                });
            });

            Schema::table('patients', function (Blueprint $table) {
                $table->foreignId('global_patient_id')->change();
                $table->string('organization_type', 30)->change();
                $table->bigInteger('organization_id')->change();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->foreignId('global_patient_id')->nullable(true)->change();
            $table->string('organization_type', 30)->nullable(true)->change();
            $table->bigInteger('organization_id')->nullable(true)->change();
        });
    }
};
