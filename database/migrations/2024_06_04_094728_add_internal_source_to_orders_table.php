<?php

use App\Enums\InternalSourceEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('internal_source')->default(InternalSourceEnum::DISTRIBUTOR->value);
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->string('internal_source')->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->removeColumn('internal_source');
        });
    }
};
