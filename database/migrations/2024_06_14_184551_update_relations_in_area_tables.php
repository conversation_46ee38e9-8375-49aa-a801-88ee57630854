<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        DB::transaction(function () {
            DB::table('territories')->whereNotIn('manager_id', function ($query) {
                $query->select('id')->from('users');
            })->update(['manager_id' => null]);

            DB::table('districts')->whereNotIn('manager_id', function ($query) {
                $query->select('id')->from('users');
            })->update(['manager_id' => null]);

            DB::table('regions')->whereNotIn('manager_id', function ($query) {
                $query->select('id')->from('users');
            })->update(['manager_id' => null]);


            Schema::table('territories', function (Blueprint $table) {
                $table->dropForeign(['manufacturer_id']);
            });

            Schema::table('districts', function (Blueprint $table) {
                $table->dropForeign(['manufacturer_id']);
            });

            Schema::table('regions', function (Blueprint $table) {
                $table->dropForeign(['manufacturer_id']);
            });

            Schema::table('territories', function (Blueprint $table) {
                $table->foreign('district_id')->references('id')->on('districts')->cascadeOnDelete();
                $table->foreign('manufacturer_id')->references('id')->on('manufacturers')->cascadeOnDelete();
                $table->foreign('manager_id')->references('id')->on('users')->nullOnDelete();
            });

            Schema::table('districts', function (Blueprint $table) {
                $table->foreign('region_id')->references('id')->on('regions')->cascadeOnDelete();
                $table->foreign('manufacturer_id')->references('id')->on('manufacturers')->cascadeOnDelete();
                $table->foreign('manager_id')->references('id')->on('users')->nullOnDelete();
            });

            Schema::table('regions', function (Blueprint $table) {
                $table->foreign('manufacturer_id')->references('id')->on('manufacturers')->cascadeOnDelete();
                $table->foreign('manager_id')->references('id')->on('users')->nullOnDelete();
            });
        });
    }

    public function down(): void
    {
        DB::transaction(function () {
            Schema::table('territories', function (Blueprint $table) {
                $table->dropForeign(['district_id']);
                $table->dropForeign(['manufacturer_id']);
                $table->dropForeign(['manager_id']);
            });

            Schema::table('districts', function (Blueprint $table) {
                $table->dropForeign(['region_id']);
                $table->dropForeign(['manufacturer_id']);
                $table->dropForeign(['manager_id']);
            });

            Schema::table('regions', function (Blueprint $table) {
                $table->dropForeign(['manufacturer_id']);
                $table->dropForeign(['manager_id']);
            });

            Schema::table('territories', function (Blueprint $table) {
                $table->foreign('manufacturer_id')->references('id')->on('manufacturers')->nullOnDelete();
            });

            Schema::table('districts', function (Blueprint $table) {
                $table->foreign('manufacturer_id')->references('id')->on('manufacturers')->nullOnDelete();
            });

            Schema::table('regions', function (Blueprint $table) {
                $table->foreign('manufacturer_id')->references('id')->on('manufacturers')->nullOnDelete();
            });
        });
    }
};
