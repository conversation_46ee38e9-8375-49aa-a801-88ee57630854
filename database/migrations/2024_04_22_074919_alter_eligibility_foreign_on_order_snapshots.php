<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_snapshots', function (Blueprint $table) {
            $table->dropForeign(['eligibility_check_id']);

            $table->foreign(['eligibility_check_id'])->references('id')->on('eligibility_checks')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_snapshots', function (Blueprint $table) {
            $table->dropForeign(['eligibility_check_id']);

            $table->foreign(['eligibility_check_id'])->references('id')->on('eligibility_checks')->cascadeOnDelete();
        });
    }
};
