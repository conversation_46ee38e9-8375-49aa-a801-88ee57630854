<?php

use App\Enums\ActivityLogTypeEnum;
use App\Enums\OrderCancellationReasonEnum;
use App\Models\ActivityLog;
use App\Models\Order;
use App\Models\OrderCancellation;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    private const REASONS_TO_RENAME = [
        'Can not touch base with patient' => OrderCancellationReasonEnum::FAILED_TO_CONNECT_WITH_CUSTOMER->value,
        'Does not want to go on therapy' => OrderCancellationReasonEnum::DOES_NOT_WANT_TO_PROCEED_WITH_ORDER->value,
        'Patient out of pocket too high' => OrderCancellationReasonEnum::PATIENT_OUT_OF_POCKET_TOO_HIGH->value,
        'Pre Auth not approved' => OrderCancellationReasonEnum::PRE_AUTHORIZATION_NOT_APPROVED->value,
        'Does not meet medical necessity' => OrderCancellationReasonEnum::DOES_NOT_MEET_MEDICAL_NECESSITY->value,
        'Out of network with the payer' => OrderCancellationReasonEnum::OUT_OF_NETWORK_WITH_THE_PAYER->value,
        'Duplicate Order' => OrderCancellationReasonEnum::DUPLICATE_ORDER->value,
        'Patient passed away' => OrderCancellationReasonEnum::PATIENT_PASSED_AWAY->value,
        'On Therapy With Another Supplier' => OrderCancellationReasonEnum::ON_THERAPY_WITH_ANOTHER_SUPPLIER->value,
        'Transferred to Pharmacy' => OrderCancellationReasonEnum::TRANSFERRED_TO_PHARMACY->value,
    ];

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->renameOrderCancellationReasons(self::REASONS_TO_RENAME);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $this->renameOrderCancellationReasons(array_flip(self::REASONS_TO_RENAME));
    }

    private function renameOrderCancellationReasons(array $reasonsToRename): void
    {
        DB::transaction(function () use ($reasonsToRename) {
            foreach ($reasonsToRename as $oldReason => $newReason) {
                OrderCancellation::query()
                    ->where('reason', $oldReason)
                    ->update(['reason' => $newReason]);
            }

            ActivityLog::query()
                ->where('activityable_type', Order::class)
                ->where('type', ActivityLogTypeEnum::CANCELED)
                ->chunkById(100, function ($activityLogs) use ($reasonsToRename) {
                    /** @var ActivityLog $activityLog */
                    foreach ($activityLogs as $activityLog) {
                        $metadata = $activityLog->metadata;

                        if (!empty($metadata['reason']) && isset($reasonsToRename[$metadata['reason']])) {
                            $metadata['reason'] = $reasonsToRename[$metadata['reason']];

                            $activityLog->metadata = $metadata;
                            $activityLog->saveQuietly();
                        }
                    }
                });
        });
    }
};
