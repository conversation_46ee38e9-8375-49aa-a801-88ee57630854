<?php

use App\Models\Manufacturer;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        DB::table('territories')
            ->leftJoin('users', 'territories.manager_id', '=', 'users.id')
            ->whereNull('users.id')
            ->update(['territories.manager_id' => null]);

        DB::table('districts')
            ->leftJoin('users', 'districts.manager_id', '=', 'users.id')
            ->whereNull('users.id')
            ->update(['districts.manager_id' => null]);

        DB::table('regions')
            ->leftJoin('users', 'regions.manager_id', '=', 'users.id')
            ->whereNull('users.id')
            ->update(['regions.manager_id' => null]);


        Schema::table('territories', function (Blueprint $table) {
            $table->dropForeign(['manufacturer_id']);
            $table->renameColumn('manufacturer_id', 'organization_id');
            $table->string('organization_type')->default(Manufacturer::class);
        });

        Schema::table('districts', function (Blueprint $table) {
            $table->dropForeign(['manufacturer_id']);
            $table->renameColumn('manufacturer_id', 'organization_id');
            $table->string('organization_type')->default(Manufacturer::class);
        });

        Schema::table('regions', function (Blueprint $table) {
            $table->dropForeign(['manufacturer_id']);
            $table->renameColumn('manufacturer_id', 'organization_id');
            $table->string('organization_type')->default(Manufacturer::class);
        });


        Schema::table('territories', function (Blueprint $table) {
            $table->string('organization_type')->default(null)->change();
        });

        Schema::table('districts', function (Blueprint $table) {
            $table->string('organization_type')->default(null)->change();
        });

        Schema::table('regions', function (Blueprint $table) {
            $table->string('organization_type')->default(null)->change();
        });
    }

    public function down(): void
    {
        Schema::table('territories', function (Blueprint $table) {
            $table->dropColumn('organization_type');
            $table->renameColumn('organization_id', 'manufacturer_id');
            $table->foreign('manufacturer_id')->references('id')->on('manufacturers')->cascadeOnDelete();
        });

        Schema::table('districts', function (Blueprint $table) {
            $table->dropColumn('organization_type');
            $table->renameColumn('organization_id', 'manufacturer_id');
            $table->foreign('manufacturer_id')->references('id')->on('manufacturers')->cascadeOnDelete();
        });

        Schema::table('regions', function (Blueprint $table) {
            $table->dropColumn('organization_type');
            $table->renameColumn('organization_id', 'manufacturer_id');
            $table->foreign('manufacturer_id')->references('id')->on('manufacturers')->cascadeOnDelete();
        });
    }
};
