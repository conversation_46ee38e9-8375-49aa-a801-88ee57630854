<?php

use App\Console\Commands\DistributorDefaultOrganizationSettings;
use App\Enums\OrganizationSettingNameEnum;
use App\Extensions\Logger;
use App\Models\OrganizationSetting;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Artisan;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Logger::info('Start command: app:distributor-default-organization-settings');
        // run console command to set default organization settings for distributors
        $result = Artisan::call(DistributorDefaultOrganizationSettings::class);
        Logger::info('Finished command: app:distributor-default-organization-settings with result: ' . $result);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        OrganizationSetting::query()
            ->where('name', OrganizationSettingNameEnum::AUTO_REPLY_TEXT)
            ->delete();
    }
};
