<?php

use App\Enums\DocumentRequestRequestTypeEnum;
use App\Enums\OrderTypeEnum;
use App\Models\Order;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            Schema::table('document_requests', function (Blueprint $table) {
                $table->string('request_type')->nullable(false)->default(DocumentRequestRequestTypeEnum::NEW_PRESCRIPTION);
                $table->unsignedBigInteger('order_id')->nullable(true)->change();

                $table->foreignId('global_patient_id')->nullable()->constrained('global_patients');
                $table->foreignId('provider_id')->nullable()->constrained();
                $table->foreignId('provider_user_id')->nullable()->constrained('users');
                $table->foreignId('distributor_id')->nullable()->constrained();
                $table->foreignId('distributor_user_id')->nullable()->constrained('users');
                $table->foreignId('facility_id')->nullable()->constrained();
                $table->foreignId('created_by')->nullable()->constrained('users');
            });

            Schema::table('order_documents', function (Blueprint $table) {
                $table->unsignedBigInteger('order_id')->nullable(true)->change();
            });

            // Migrate relations
            DB::table('orders')->chunkById(100, function (Collection $orders) {
                $orders->each(function ($order) {
                    DB::table('document_requests')
                        ->where('order_id', $order->id)
                        ->update([
                            'global_patient_id' => $order->global_patient_id,
                            'provider_id' => $order->provider_id,
                            'provider_user_id' => $order->provider_user_id,
                            'distributor_id' => $order->distributor_id,
                            'distributor_user_id' => $order->distributor_user_id,
                            'facility_id' => $order->facility_id,
                            'created_by' => $order->created_by,
                        ]);
                });
            });

            $renewalOrderIdsQuery = DB::table('orders')->where('type', OrderTypeEnum::PRESCRIPTION_RENEWAL)->select('id');

            // unlink renewal requests from orders
            DB::table('document_requests')
                ->whereIn('order_id', $renewalOrderIdsQuery)
                ->update([
                    'request_type' => DocumentRequestRequestTypeEnum::PRESCRIPTION_RENEWAL,
                    'order_id' => null,
                ]);

            // unlink renewal documents from orders
            DB::table('order_documents')
                ->whereIn('order_id', $renewalOrderIdsQuery)
                ->update([
                    'order_id' => null,
                ]);

            // Delete renewal orders relations
            DB::table('activity_logs')
                ->where('activityable_type', Order::class)
                ->whereIn('activityable_id', $renewalOrderIdsQuery)
                ->delete();
            DB::table('order_cancellations')->whereIn('order_id', $renewalOrderIdsQuery)->delete();
            DB::table('order_snapshots')->whereIn('order_id', $renewalOrderIdsQuery)->delete();
            DB::table('order_shippings')->whereIn('order_id', $renewalOrderIdsQuery)->delete();

            // Delete renewal orders
            DB::table('orders')->where('type', OrderTypeEnum::PRESCRIPTION_RENEWAL)->delete();

            Schema::table('document_requests', function (Blueprint $table) {
                $table->string('request_type')->nullable(false)->default(null)->change();
                $table->unsignedBigInteger('global_patient_id')->nullable(false)->change();
                $table->unsignedBigInteger('distributor_id')->nullable(false)->change();
                $table->unsignedBigInteger('created_by')->nullable(false)->change();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::transaction(function () {
            Schema::table('document_requests', function (Blueprint $table) {
                $table->dropColumn('request_type');
                $table->unsignedBigInteger('order_id')->nullable(false)->change();

                $table->dropConstrainedForeignId('global_patient_id');
                $table->dropConstrainedForeignId('provider_id');
                $table->dropConstrainedForeignId('provider_user_id');
                $table->dropConstrainedForeignId('distributor_id');
                $table->dropConstrainedForeignId('distributor_user_id');
                $table->dropConstrainedForeignId('facility_id');
            });

            Schema::table('order_documents', function (Blueprint $table) {
                $table->unsignedBigInteger('order_id')->nullable(false)->change();
            });
        });
    }
};
