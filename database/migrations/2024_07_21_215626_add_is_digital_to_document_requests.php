<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('document_requests', function (Blueprint $table) {
            $table->boolean('is_digital')->default(false);
            $table->dateTime('follow_up_at')->nullable();
        });
        Schema::table('document_requests', function (Blueprint $table) {
            $table->boolean('is_digital')->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('document_requests', function (Blueprint $table) {
            $table->dropColumn('is_digital');
            $table->dropColumn('follow_up_at');
        });
    }
};
