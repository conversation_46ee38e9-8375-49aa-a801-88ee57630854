<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_document_requests', function (Blueprint $table) {
            $table->date('appointment_confirmation_date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_document_requests', function (Blueprint $table) {
            $table->dropColumn('appointment_confirmation_date');
        });
    }
};
