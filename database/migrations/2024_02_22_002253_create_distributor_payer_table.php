<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('distributor_payer', function (Blueprint $table) {
            $table->foreignId('payer_id')->constrained('payers')->cascadeOnDelete();
            $table->foreignId('distributor_id')->constrained('distributors')->cascadeOnDelete();
            $table->boolean('is_supported')->default(false);
            $table->json('payer_types');
            $table->json('payer_plan_types')->nullable();
            $table->json('serviceable_states');
            $table->string('sor_id', 30)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('distributor_payer');
    }
};
