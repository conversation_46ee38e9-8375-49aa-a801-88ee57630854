<?php

use App\Enums\MessageTemplateStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('message_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('distributor_campaign_id')->constrained('distributor_campaigns')->cascadeOnDelete();
            $table->string('name');
            $table->text('body');
            $table->unsignedBigInteger('phone_number')->nullable();
            $table->string('status')->default(MessageTemplateStatusEnum::PENDING->value);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('message_templates');
    }
};
