<?php

use App\Enums\LeadCanceledReasonEnum;
use App\Extensions\Logger;
use App\Models\Lead;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    private const REASONS_TO_RENAME = [
        'could_not_connect_with_the_patient' => LeadCanceledReasonEnum::FAILED_TO_CONNECT_WITH_THE_CUSTOMER->value,
        'patient_does_not_want_to_go_on_therapy' => LeadCanceledReasonEnum::CUSTOMER_DOES_NOT_WANT_TO_GO_ON_THERAPY->value,
        'patient_out_of_pocket_too_high' => LeadCanceledReasonEnum::OUT_OF_POCKET_TOO_HIGH->value,
        'went_with_another_distributor' => LeadCanceledReasonEnum::ON_THERAPY_WITH_ANOTHER_SUPPLIER->value,
    ];
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $reasonsToDelete = [
            'cannot_support_location',
            'not_qualified_other',
        ];
        $reasonsToDeleteCount = Lead::query()
            ->whereIn('canceled_reason', $reasonsToDelete)
            ->count();

        Logger::info('Count of leads with deleted canceled reasons' . $reasonsToDeleteCount);

        $this->renameLeadCanceledReasons(self::REASONS_TO_RENAME);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $this->renameLeadCanceledReasons(array_flip(self::REASONS_TO_RENAME));
    }

    private function renameLeadCanceledReasons(array $reasonsToRename): void
    {
        DB::transaction(function () use ($reasonsToRename) {
            foreach ($reasonsToRename as $oldReason => $newReason) {
                Lead::query()
                    ->where('canceled_reason', $oldReason)
                    ->update(['canceled_reason' => $newReason]);
            }
        });
    }
};
