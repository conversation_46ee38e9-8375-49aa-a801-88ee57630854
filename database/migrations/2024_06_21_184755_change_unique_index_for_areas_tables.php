<?php

use App\Models\District;
use App\Models\Region;
use App\Models\Territory;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $uniqueRegions = Region::select('organization_type', 'organization_id', 'name', DB::raw('MIN(id) as min_id'))
            ->groupBy('organization_type', 'organization_id', 'name')
            ->pluck('min_id');

        Region::whereNotIn('id', $uniqueRegions)->delete();

        $uniqueDistricts = District::select('organization_type', 'organization_id', 'name', DB::raw('MIN(id) as min_id'))
            ->groupBy('organization_type', 'organization_id', 'name')
            ->pluck('min_id');

        District::whereNotIn('id', $uniqueDistricts)->delete();

        $uniqueTerritories = Territory::select('organization_type', 'organization_id', 'name', DB::raw('MIN(id) as min_id'))
            ->groupBy('organization_type', 'organization_id', 'name')
            ->pluck('min_id');

        Territory::whereNotIn('id', $uniqueTerritories)->delete();


        Schema::table('regions', function (Blueprint $table) {
            $table->unique(['organization_type', 'organization_id', 'name']);
        });

        Schema::table('districts', function (Blueprint $table) {
            $table->unique(['organization_type', 'organization_id', 'name']);
        });

        Schema::table('territories', function (Blueprint $table) {
            $table->unique(['organization_type', 'organization_id', 'name']);
        });
    }

    public function down(): void
    {
        Schema::table('regions', function (Blueprint $table) {
            $table->dropUnique(['organization_type', 'organization_id', 'name']);
        });

        Schema::table('districts', function (Blueprint $table) {
            $table->dropUnique(['organization_type', 'organization_id', 'name']);
        });

        Schema::table('territories', function (Blueprint $table) {
            $table->dropUnique(['organization_type', 'organization_id', 'name']);
        });
    }
};
