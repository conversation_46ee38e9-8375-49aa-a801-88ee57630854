<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_document_request_history', function (Blueprint $table) {
            $table->id();
            $table->string('type', 30);
            $table->foreignId('order_document_request_id')->constrained('order_document_requests')->cascadeOnDelete();
            $table->foreignId('fax_id')->nullable()->constrained('faxes')->nullOnDelete();
            $table->foreignId('order_document_id')->nullable()->constrained('order_documents')->nullOnDelete();
            $table->foreignId('user_id')->nullable()->constrained('users')->nullOnDelete();
            $table->json('details')->nullable();
            $table->timestamp('activity_at')->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_document_request_history');
    }
};
