<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lead_payer', function (Blueprint $table) {
            $table->boolean('is_covered')->default(false)->after('in_max_oop_remaining');
            $table->boolean('in_is_covered')->default(false)->after('is_covered');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lead_payer', function (Blueprint $table) {
            $table->dropColumn(['is_covered', 'in_is_covered']);
        });
    }
};
