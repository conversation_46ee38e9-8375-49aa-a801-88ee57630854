<?php

use App\Enums\OrderDocumentSourceEnum;
use App\Enums\OrderDocumentTypeEnum;
use App\Models\OrderDocument;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        OrderDocument::query()
            ->where('type', OrderDocumentTypeEnum::SHIPMENT_STATUS)
            ->update(['source' => OrderDocumentSourceEnum::DISTRIBUTOR_USER]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        OrderDocument::query()
            ->where('type', OrderDocumentTypeEnum::SHIPMENT_STATUS)
            ->update(['source' => OrderDocumentSourceEnum::SYSTEM]);
    }
};
