<?php

use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            Schema::rename('user_signatures', 'signatures');
            Schema::table('signatures', function (Blueprint $table) {
                $table->string('signable_type')->default(User::class);
                $table->dropForeign('user_signatures_user_id_foreign');
                $table->renameColumn('user_id', 'signable_id');
                $table->renameIndex('user_signatures_pkey', 'signatures_pkey');
                $table->dropIndex('user_signatures_user_id_index');
                $table->index(['signable_id', 'signable_type']);
                $table->dropUnique('user_signatures_user_id_unique');
                $table->unique(['signable_id', 'signable_type']);
            });

            Schema::table('signatures', function (Blueprint $table) {
                $table->string('signable_type')->default(null)->change();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::transaction(function () {
            Schema::rename('signatures', 'user_signatures');
            Schema::table('user_signatures', function (Blueprint $table) {
                $table->dropUnique('signatures_signable_id_signable_type_unique');
                $table->renameColumn('signable_id', 'user_id');
                $table->foreign('user_id')->references('id')->on('users');
                $table->dropIndex('signatures_signable_id_signable_type_index');
                $table->dropColumn('signable_type');
                $table->renameIndex('signatures_pkey', 'user_signatures_pkey');
                $table->unique('user_id');
                $table->index('user_id', 'user_signatures_user_id_index');
            });
        });
    }
};
