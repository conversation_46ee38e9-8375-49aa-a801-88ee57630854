<?php

use App\Enums\OrderTypeEnum;
use App\Models\MedicalPolicyForm;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            Schema::table('medical_policy_forms', function (Blueprint $table) {
                $table->string('order_type', 30)->default(OrderTypeEnum::NEW_PRESCRIPTION->value);
                $table->string('payer_type', 30)->nullable();
            });

            $forms = DB::table('cms_hcpc_medical_policy_form')->distinct('medical_policy_form_id')->get();

            foreach ($forms as $form) {
                DB::statement('UPDATE medical_policy_forms SET order_type = \'' . $form->order_type . '\' WHERE id = ' . $form->medical_policy_form_id);
            }

            Schema::table('cms_hcpc_medical_policy_form', function (Blueprint $table) {
                $table->dropColumn('order_type');
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::transaction(function () {
            Schema::table('cms_hcpc_medical_policy_form', function (Blueprint $table) {
                $table->string('order_type')->default(OrderTypeEnum::NEW_PRESCRIPTION->value);
            });

            $forms = MedicalPolicyForm::all();

            foreach ($forms as $form) {
                DB::statement('UPDATE cms_hcpc_medical_policy_form SET order_type = \'' . $form->order_type->value . '\' WHERE medical_policy_form_id = ' . $form->id);
            }

            Schema::table('medical_policy_forms', function (Blueprint $table) {
                $table->dropColumn(['order_type', 'payer_type']);
            });
        });
    }
};
