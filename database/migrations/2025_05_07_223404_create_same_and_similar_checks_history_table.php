<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('same_and_similar_checks_history', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('same_and_similar_check_id');
            $table->unsignedBigInteger('request_id')->nullable();
            $table->bigInteger('patient_id')->nullable();
            $table->string('patient_gender')->nullable();
            $table->string('patient_first_name')->nullable();
            $table->string('patient_last_name')->nullable();
            $table->string('patient_dob')->nullable();
            $table->string('patient_state_code')->nullable();
            $table->smallInteger('request_attempts')->default(0);
            $table->string('status');
            $table->string('message')->nullable();
            $table->foreignId('order_id')->nullable()->constrained();
            $table->foreignId('product_id')->nullable()->constrained();
            $table->json('response')->nullable();
            $table->json('hcpc_codes')->nullable();
            $table->dateTime('response_expected_at')->nullable();
            $table->dateTime('response_expected_date_time')->nullable();
            $table->bigInteger('response_expected_time')->nullable();
            $table->bigInteger('created_by')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('same_and_similar_checks_history');
    }
};
