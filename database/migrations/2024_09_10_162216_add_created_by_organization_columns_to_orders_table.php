<?php

use App\Models\Distributor;
use App\Models\Manufacturer;
use App\Models\Order;
use App\Models\Provider;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            Schema::table('orders', function (Blueprint $table) {
                $table->nullableMorphs('created_by_organization');
            });

            $this->setCreatedByOrganizationForAllOrders();

            Schema::table('orders', function (Blueprint $table) {
                $table->unsignedBigInteger('created_by_organization_id')->change();
                $table->string('created_by_organization_type')->change();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropMorphs('created_by_organization');
        });
    }

    private function setCreatedByOrganizationForAllOrders(): void
    {
        Order::query()->with([
            'createdBy' => function ($query) {
                $query->withTrashed();
            },
        ])->chunkById(500, function ($orders) {
            /** @var Order $order */
            foreach ($orders as $order) {
                $organization = $this->getOrganization($order->createdBy);

                $order->createdByOrganization()->associate($organization);
                $order->save();
            }
        });
    }

    public function getOrganization(User $user): Distributor|Manufacturer|Provider|null
    {
        return match (true) {
            $user->isDistributorUser() => $user->distributor,
            $user->isProviderUser() => $user->providers()->first(),
            $user->isManufacturerUser() => $user->manufacturer,
        };
    }
};
