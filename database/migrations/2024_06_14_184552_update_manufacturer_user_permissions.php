<?php

use App\Enums\UserPermissionEnum;
use Illuminate\Database\Migrations\Migration;
use Spatie\Permission\Models\Permission;

return new class extends Migration
{
    private array $oldPermissions = [
        'can_manage_region',
        'can_manage_district',
        'can_manage_territory',
        'can_read_other_regions',
        'can_read_other_districts',
        'can_read_other_territories',
    ];

    public function up(): void
    {
        // foreach ($this->oldPermissions as $permission) {
        //     Permission::where('name', $permission)->where('guard_name', config('auth.defaults.guard'))->delete();
        // }
        //
        // foreach (UserPermissionEnum::getValues() as $permission) {
        //     Permission::firstOrCreate(['name' => $permission, 'guard_name' => config('auth.defaults.guard')]);
        // }
    }

    public function down(): void
    {
        // foreach (UserPermissionEnum::getValues() as $permission) {
        //     Permission::where('name', $permission)->where('guard_name', config('auth.defaults.guard'))->delete();
        // }
        //
        // foreach ($this->oldPermissions as $permission) {
        //     Permission::firstOrCreate(['name' => $permission, 'guard_name' => config('auth.defaults.guard')]);
        // }
    }
};
