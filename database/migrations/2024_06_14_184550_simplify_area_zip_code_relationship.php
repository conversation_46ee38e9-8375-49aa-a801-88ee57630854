<?php

use App\Models\Territory;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::rename('area_zip_code', 'territory_zip_code');

        Schema::table('territory_zip_code', function (Blueprint $table) {
            $table->dropColumn('area_type');
            $table->renameColumn('area_id', 'territory_id');
            $table->foreign('territory_id')->references('id')->on('territories')->cascadeOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('territory_zip_code', function (Blueprint $table) {
            $table->dropForeign(['territory_id']);
        });

        Schema::rename('territory_zip_code', 'area_zip_code');

        Schema::table('area_zip_code', function (Blueprint $table) {
            $table->string('area_type')->default(Territory::class);
            $table->renameColumn('territory_id', 'area_id');
        });
    }
};
