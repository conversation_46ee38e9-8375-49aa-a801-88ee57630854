<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('patient_insurance_card_request_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_insurance_card_request_id')->constrained()->onDelete('cascade');
            $table->string('status', 20);
            $table->timestamp('expiration_date');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('created_at');

            // Indexes for performance
            $table->index(['patient_insurance_card_request_id', 'created_at']);
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('patient_insurance_card_request_history');
    }
};
