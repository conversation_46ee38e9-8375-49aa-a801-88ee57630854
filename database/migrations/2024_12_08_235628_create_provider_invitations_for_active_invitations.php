<?php

use App\Enums\IdentificationTokenTypeEnum;
use App\Enums\LogGroupEnum;
use App\Extensions\Logger;
use App\Models\Distributor;
use App\Models\IdentificationToken;
use App\Models\Pivot\DistributorProviderPivot;
use App\Models\ProviderUser;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// NOTE: This migration is used to create missing provider invitations and can be safely removed in future
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            IdentificationToken::query()
                ->with('identifiable')
                ->where('token_type', IdentificationTokenTypeEnum::INVITATION)
                ->whereHasMorph('identifiable', User::class, function ($query) {
                    $query->where('entity_user_type', ProviderUser::class);
                })->each(function (IdentificationToken $token) {
                    $providerUser = $token->identifiable;
                    $provider = $providerUser->providers()->wherePivot('owner', true)->first();

                    if ($provider?->createdBy?->isDistributorUser()) {
                        $distributorUser = $provider->createdBy;
                        $providerDistributorsIdQuery = DistributorProviderPivot::query()
                            ->select('distributor_id')
                            ->where('provider_id', $provider->id);

                        // for now we assume that a distributor user can only have one distributor
                        /** @var Distributor $distributor */
                        $distributor = $distributorUser
                            ->distributors()
                            ->whereIn('distributors.id', $providerDistributorsIdQuery)
                            ->first();

                        if (!$distributor) {
                            Logger::info(
                                "Can't create provider invitation for provider: {$provider->name} as distributor is missing",
                                LogGroupEnum::DEFAULT,
                                [
                                    'provider_id' => $provider->id,
                                    'distributor_user_id' => $distributorUser->id,
                                    'token_id' => $token->id,
                                ],
                            );

                            return;
                        }

                        $distributor->providerInvitations()->create([
                            'provider_id' => $provider->id,
                            'invited_by' => $distributorUser->id,
                            'sent_at' => $token->created_at,
                        ]);

                        Logger::info(
                            "Creating missing provider invitation for provider: {$provider->name} and distributor: {$distributor->name}",
                            LogGroupEnum::DEFAULT,
                            [
                                'provider_id' => $provider->id,
                                'distributor_id' => $distributor->id,
                                'distributor_user_id' => $distributorUser->id,
                            ],
                        );
                    }
                });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('provider_invitations_for_active_invitations');
    }
};
