<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('message_configurations', function (Blueprint $table) {
            $table->id();
            $table->string('organization_type');
            $table->unsignedBigInteger('organization_id');
            $table->string('scenario');
            $table->string('condition');
            $table->text('content')->nullable();
            $table->json('placeholders')->nullable();
            $table->boolean('active')->default(true);
            $table->softDeletes();
            $table->timestamps();

            $table->unique(
                ['organization_type', 'organization_id', 'scenario', 'condition'],
                'unique_msg_config_per_org_context',
            );

            $table->index(['scenario', 'condition'], 'idx_scenario_condition_lookup');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('message_configurations');
    }
};
