<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Update columns in the leads table
        Schema::table('leads', function (Blueprint $table) {

            $table->boolean('sms_enabled')->default(false)->after('communications_text');
            $table->boolean('email_enabled')->default(false)->after('sms_enabled');
            $table->boolean('call_enabled')->default(false)->after('email_enabled');
        });

        DB::table('leads')->update([
            'sms_enabled' => DB::raw('communications'),
            'email_enabled' => DB::raw('email_communications'),
        ]);

    }

    public function down(): void
    {
        // Revert the changes made in the up method
        Schema::table('leads', function (Blueprint $table) {

            $table->dropColumn('sms_enabled');
            $table->dropColumn('email_enabled');
            $table->dropColumn('call_enabled');
        });
    }
};
