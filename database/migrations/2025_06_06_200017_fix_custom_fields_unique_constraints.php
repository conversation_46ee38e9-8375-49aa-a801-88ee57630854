<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * PRODUCTION FIX - Replace regular unique constraints with partial unique indexes
     * that only apply to non-soft-deleted records
     */
    public function up(): void
    {
        // Drop existing unique constraints
        Schema::table('custom_fields', function (Blueprint $table) {
            $table->dropUnique('custom_fields_campaign_display_and_system_unique');
        });

        // Create partial unique indexes using raw SQL (PostgreSQL specific)
        // These only apply to non-deleted records
        DB::statement('
            CREATE UNIQUE INDEX custom_fields_campaign_display_and_system_unique
            ON custom_fields (distributor_campaign_id, display_name, system_name)
            WHERE deleted_at IS NULL
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the partial unique indexes
        DB::statement('DROP INDEX IF EXISTS custom_fields_campaign_display_and_system_unique');

        // Recreate original unique constraints
        Schema::table('custom_fields', function (Blueprint $table) {
            $table->unique(['distributor_campaign_id', 'display_name', 'system_name'], 'custom_fields_campaign_display_and_system_unique');
        });
    }
};
