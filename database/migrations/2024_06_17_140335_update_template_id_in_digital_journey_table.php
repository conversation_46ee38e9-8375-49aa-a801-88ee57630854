<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            DB::statement('UPDATE digital_journeys SET communication_method = \'message_template\' WHERE communication_method=\'sms\'');
            DB::statement('UPDATE digital_journeys SET communication_method = \'email_template\' WHERE communication_method=\'email\'');

            Schema::table('digital_journeys', function (Blueprint $table) {
                $table->dropForeign('digital_journeys_message_template_id_foreign');
                $table->renameColumn('message_template_id', 'template_id');
                $table->renameColumn('communication_method', 'template_type');
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::transaction(function () {
            DB::statement('UPDATE digital_journeys SET template_type = \'sms\' WHERE template_type=\'message_template\'');
            Schema::table('digital_journeys', function (Blueprint $table) {
                $table->renameColumn('template_id', 'message_template_id');
                $table->foreign('message_template_id')->references('id')->on('message_templates')->cascadeOnDelete();
                $table->renameColumn('template_type', 'communication_method');
            });
        });
    }
};
