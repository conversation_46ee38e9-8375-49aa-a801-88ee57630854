<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_escalations', function (Blueprint $table) {
            $table->dateTime('assigned_distributor_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_escalations', function (Blueprint $table) {
            $table->dropColumn('assigned_distributor_at');
        });
    }
};
