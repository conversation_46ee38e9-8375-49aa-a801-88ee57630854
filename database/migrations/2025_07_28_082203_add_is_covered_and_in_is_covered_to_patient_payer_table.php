<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patient_payer', function (Blueprint $table) {
            $table->boolean('is_covered')->default(false);
            $table->boolean('in_is_covered')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patient_payer', function (Blueprint $table) {
            $table->dropColumn(['is_covered', 'in_is_covered']);
        });
    }
};
