<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement(
            'UPDATE addresses SET zip = substr(zip, 1, 5) || \'-\' || substr(zip, 6) WHERE LENGTH(zip) = 9',
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('UPDATE addresses SET zip = REPLACE(zip, \'-\', \'\')');
    }
};
