<?php

use App\Models\DistributorCampaign;
use App\Models\OrganizationSetting;
use App\Utils\PhoneNumberConverter;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            Schema::table('distributor_campaigns', function (Blueprint $table) {
                $table->text('auto_reply_text')->nullable();
            });

            DistributorCampaign::query()
                ->get()
                ->each(function (DistributorCampaign $campaign) {
                    $phoneNumber = PhoneNumberConverter::formatPhoneNumber($campaign->distributor->phone);
                    $autoReplyText = sprintf(
                        OrganizationSetting::DEFAULT_AUTO_REPLY_TEXT_FORMAT,
                        $phoneNumber,
                    );

                    $campaign->update([
                        'auto_reply_text' => $autoReplyText,
                    ]);
                });

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('distributor_campaigns', function (Blueprint $table) {
            $table->dropColumn('auto_reply_text');
        });
    }
};
