<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('territory_zip_code', function (Blueprint $table) {
            $table->unique(['zip_code_id', 'territory_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('territory_zip_code', function (Blueprint $table) {
            $table->dropUnique(['zip_code_id', 'territory_id']);
        });
    }
};
