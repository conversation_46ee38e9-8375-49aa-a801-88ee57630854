<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->setPayers('patient_payer');
        $this->setPayers('lead_payer');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patient_payer', function (Blueprint $table) {
            $table->string('user_defined_payer_type')->nullable(true)->change();
        });

        Schema::table('lead_payer', function (Blueprint $table) {
            $table->string('user_defined_payer_type')->nullable(true)->change();
        });
    }

    public function setPayers(string $table): void
    {
        DB::table($table)
            ->whereNull('user_defined_payer_type')
            ->eachById(function ($row) use ($table) {
                $payer = DB::table('payers')
                    ->where('id', $row->payer_id)
                    ->first();

                if ($payer) {
                    DB::table($table)
                        ->where('id', $row->id)
                        ->update([
                            'user_defined_payer_type' => $payer->internal_type,
                        ]);
                }
            });

        Schema::table($table, function (Blueprint $table) {
            $table->string('user_defined_payer_type')->nullable(false)->change();
        });
    }
};
