<?php

use App\Enums\MessageConfigurationConditionEnums;
use App\Enums\MessageConfigurationScenarioEnums;
use App\Models\Distributor;
use App\Models\MessageConfiguration;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add PENDING_DOCUMENT_QA message configuration for all distributors
        $distributors = Distributor::all();

        foreach ($distributors as $distributor) {
            MessageConfiguration::updateOrCreate([
                'organization_type' => Distributor::class,
                'organization_id' => $distributor->id,
                'scenario' => MessageConfigurationScenarioEnums::ORDER_STATUS->value,
                'condition' => MessageConfigurationConditionEnums::PENDING_DOCUMENT_QA->value,
            ], [
                'content' => null, // Default content is empty/null
                'placeholders' => null,
                'active' => false, // Not active by default
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
