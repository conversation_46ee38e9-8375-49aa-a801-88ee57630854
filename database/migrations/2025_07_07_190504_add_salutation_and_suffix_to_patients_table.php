<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->string('salutation', 255)->nullable()->after('id');
            $table->string('suffix', 255)->nullable()->after('last_name');
        });

        // Add composite search index for performance
        Schema::table('patients', function (Blueprint $table) {
            $table->index([
                DB::raw('lower(salutation)'),
                DB::raw('lower(first_name)'),
                DB::raw('lower(middle_name)'),
                DB::raw('lower(last_name)'),
                DB::raw('lower(suffix)'),
            ], 'idx_patient_search');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->dropIndex('idx_patient_search');
            $table->dropColumn(['salutation', 'suffix']);
        });
    }
};
