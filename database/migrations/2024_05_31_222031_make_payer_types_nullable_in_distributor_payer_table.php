<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('distributor_payer', function (Blueprint $table) {
            $table->json('payer_types')->nullable()->change();
            $table->json('serviceable_states')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('distributor_payer', function (Blueprint $table) {
            $table->json('serviceable_states')->change();
            $table->json('payer_types')->change();
        });
    }
};
