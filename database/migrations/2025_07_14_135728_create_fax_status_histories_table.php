<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fax_status_histories', function (Blueprint $table) {
            $table->id();
            $table->uuid('fax_sid');
            $table->string('fax_status');
            $table->string('fax_status_description');
            $table->dateTimeTz('fax_status_timestamps');
            $table->json('fax_metadata')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fax_status_histories');
    }
};
