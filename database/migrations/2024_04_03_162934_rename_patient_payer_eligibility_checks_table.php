<?php

use App\Models\Pivot\PatientPayerPivot;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::transaction(function () {
            Schema::rename('patient_payer_eligibility_checks', 'eligibility_checks');
            Schema::table('eligibility_checks', function (Blueprint $table) {
                $table->string('eligibilitable_type')->default(PatientPayerPivot::class);
                $table->dropForeign('patient_payer_eligibility_checks_payer_service_id_foreign');
                $table->foreign('payer_service_id')->references('id')->on('payer_services')->nullOnDelete();
                $table->dropForeign('patient_payer_eligibility_checks_patient_payer_id_foreign');
                $table->renameColumn('patient_payer_id', 'eligibilitable_id');
                $table->index(['eligibilitable_type', 'eligibilitable_id']);
            });

            Schema::table('eligibility_checks', function (Blueprint $table) {
                $table->string('eligibilitable_type')->default(null)->change();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::transaction(function () {
            Schema::rename('eligibility_checks', 'patient_payer_eligibility_checks');
            Schema::table('patient_payer_eligibility_checks', function (Blueprint $table) {
                $table->dropIndex('eligibility_checks_eligibilitable_type_eligibilitable_id_index');
                $table->dropColumn('eligibilitable_type');
                $table->renameColumn('eligibilitable_id', 'patient_payer_id');
                $table->foreign('patient_payer_id')->references('id')->on('patient_payer')->cascadeOnDelete();
                $table->dropForeign('eligibility_checks_payer_service_id_foreign');
                $table->foreign('payer_service_id')->references('id')->on('payer_services')->nullOnDelete();
            });
        });
    }
};
