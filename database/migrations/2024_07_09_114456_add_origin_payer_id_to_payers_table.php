<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payers', function (Blueprint $table) {
            $table->foreignId('origin_payer_id')->nullable()->constrained('payers');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payers', function (Blueprint $table) {
            $table->dropForeign(['origin_payer_id']);
            $table->dropColumn('origin_payer_id');
        });
    }
};
