<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contact_numbers', function (Blueprint $table) {
            $table->smallInteger('phone_extension', unsigned : true)->nullable();
        });

        Schema::table('providers', function (Blueprint $table) {
            $table->smallInteger('phone_extension', unsigned : true)->nullable();
        });

        Schema::table('users', function (Blueprint $table) {
            $table->smallInteger('phone_extension', unsigned : true)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contact_numbers', function (Blueprint $table) {
            $table->dropColumn('phone_extension');
        });

        Schema::table('providers', function (Blueprint $table) {
            $table->dropColumn('phone_extension');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('phone_extension');
        });
    }
};
