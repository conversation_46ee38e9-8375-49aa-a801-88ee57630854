<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('file_imports', function (Blueprint $table) {
            $table->integer('count_rows')->default(0)->after('execution_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('file_imports', function (Blueprint $table) {
            $table->dropColumn('count_rows');
        });
    }
};
