<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cms_hcpc_related_cms_hcpc', function (Blueprint $table) {
            $table->foreignId('cms_hcpc_id')->constrained();
            $table->foreignId('related_cms_hcpc_id')->constrained('cms_hcpcs');

            $table->index('cms_hcpc_id');
            $table->index('related_cms_hcpc_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cms_hcpc_related_cms_hcpc');
    }
};
