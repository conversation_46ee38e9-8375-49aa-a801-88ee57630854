<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('patient_provider_users', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_id')->constrained();
            $table->foreignId('provider_user_id')->constrained('users');
            $table->foreignId('updated_by')->constrained('users');
            $table->string('association_type')->nullable();
            $table->foreignId('distributor_id')->constrained();
            $table->timestamps();

            $table->unique(
                ['patient_id', 'provider_user_id', 'association_type'],
                'patient_provider_user_unique',
            );
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('patient_provider_users');
    }
};
