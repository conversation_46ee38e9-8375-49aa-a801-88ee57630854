<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Add new communication columns to the patients table
        Schema::table('patients', function (Blueprint $table) {
            $table->boolean('sms_enabled')->default(false)->after('mobile_communications');
            $table->boolean('email_enabled')->default(false)->after('sms_enabled');
            $table->boolean('call_enabled')->default(false)->after('text_enabled');
        });

        // Update the new columns based on the mobile_communications value
        DB::statement('
            UPDATE patients
            SET 
                sms_enabled = CASE WHEN mobile_communications = true THEN true ELSE false END,
                email_enabled = CASE WHEN mobile_communications = true THEN true ELSE false END,
                call_enabled = CASE WHEN mobile_communications = true THEN true ELSE false END
        ');
    }

    public function down(): void
    {
        // Remove the communication columns from the patients table
        Schema::table('patients', function (Blueprint $table) {
            $table->dropColumn(['sms_enabled', 'email_enabled', 'call_enabled']);
        });
    }
};
