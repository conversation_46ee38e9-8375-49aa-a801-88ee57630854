<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cms_hcpc_medical_policy_form', function (Blueprint $table) {
            $table->dropIndex('cms_hcpc_medical_policy_form_condition_index');
            $table->dropUnique('cms_hcpc_medical_policy_form_unique');

            $table->dropColumn('payer_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cms_hcpc_medical_policy_form', function (Blueprint $table) {
            $table->string('payer_type');

            $table->unique(['cms_hcpc_id', 'order_type', 'payer_type'], 'cms_hcpc_medical_policy_form_unique');
            $table->index(['cms_hcpc_id', 'order_type', 'payer_type'], 'cms_hcpc_medical_policy_form_condition_index');
        });
    }
};
