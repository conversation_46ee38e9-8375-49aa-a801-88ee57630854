<?php

use App\Enums\EmailTemplateBuilderType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmailTemplateBuildersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('distributor_email_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->longText('html_content');
            $table->json('json_content');
            $table->enum('type', EmailTemplateBuilderType::values());
            $table->unsignedBigInteger('distributor_id');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('distributor_id')->references('id')->on('distributors')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('distributor_email_templates');
    }
}
