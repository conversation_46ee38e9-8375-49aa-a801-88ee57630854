<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Str;
use <PERSON><PERSON>\Passport\Client as OauthClient;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        OauthClient::query()->firstOrCreate(['name' => 'password_client', 'user_id' => null], [
            'secret' => Str::random(40),
            'provider' => 'users',
            'personal_access_client' => false,
            'password_client' => true,
            'revoked' => false,
            'redirect' => '',
        ]);

        Permission::query()->update(['guard_name' => 'int-api']);
        Role::query()->update(['guard_name' => 'int-api']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Permission::query()->update(['guard_name' => 'web']);
        Role::query()->update(['guard_name' => 'web']);
    }
};
