<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payer_services', function (Blueprint $table) {
            $table->dropUnique('payer_services_payer_service_type_ch_service_id_unique');

            $table->dropColumn('ch_service_id');
            $table->dropColumn('accepts_secondary');
            $table->dropColumn('enrollment_required');
            $table->dropColumn('enrollment_method');
            $table->dropColumn('enrollment_type');
            $table->dropColumn('report_level');
            $table->dropColumn('states');
            $table->dropColumn('notes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payer_services', function (Blueprint $table) {
            $table->unique(['payer_service_type', 'ch_service_id'], 'payer_services_payer_service_type_ch_service_id_unique');

            $table->string('ch_service_id', 20)->nullable();
            $table->boolean('accepts_secondary')->nullable();
            $table->boolean('enrollment_required')->nullable();
            $table->string('enrollment_method')->nullable();
            $table->string('enrollment_type')->nullable();
            $table->string('report_level')->nullable();
            $table->string('states')->nullable();
            $table->text('notes')->nullable();
        });
    }
};
