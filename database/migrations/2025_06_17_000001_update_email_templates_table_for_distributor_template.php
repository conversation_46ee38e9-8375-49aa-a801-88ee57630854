<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('email_templates', function (Blueprint $table) {
            // Rename columns and append '_old'
            $table->renameColumn('name', 'name_old');
            $table->string('name_old')->nullable()->change();
            $table->renameColumn('template_title', 'template_title_old');
            $table->json('template_title_old')->nullable()->change();
            $table->renameColumn('template_body', 'template_body_old');
            $table->json('template_body_old')->nullable()->change();
            $table->renameColumn('template_footer', 'template_footer_old');
            $table->json('template_footer_old')->nullable()->change();
            // Add new distributor_template_id column
            $table->unsignedBigInteger('distributor_template_id')->nullable()->after('id');
            $table->foreign('distributor_template_id')
                ->references('id')
                ->on('distributor_email_templates')
                ->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('email_templates', function (Blueprint $table) {
            // Drop foreign key and column
            $table->dropForeign(['distributor_template_id']);
            $table->dropColumn('distributor_template_id');
            // Rename columns back to original
            $table->renameColumn('name_old', 'name');
            $table->renameColumn('template_title_old', 'template_title');
            $table->renameColumn('template_body_old', 'template_body');
            $table->renameColumn('template_footer_old', 'template_footer');
        });
    }
};
