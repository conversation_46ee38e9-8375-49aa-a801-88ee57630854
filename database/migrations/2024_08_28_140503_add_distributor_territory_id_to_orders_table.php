<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->foreignId('distributor_territory_id')->nullable()->constrained('territories')->nullOnDelete();
        });

        Artisan::call('distributor:update-territory');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropForeign(['distributor_territory_id']);
            $table->dropColumn('distributor_territory_id');
        });
    }
};
