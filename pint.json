{"preset": "psr12", "rules": {"new_with_braces": {"anonymous_class": false, "named_class": false}, "class_definition": {"multi_line_extends_each_single_line": true, "single_item_single_line": true, "single_line": true}, "curly_braces_position": {"anonymous_classes_opening_brace": "next_line_unless_newline_at_signature_end"}, "trailing_comma_in_multiline": {"elements": ["arguments", "arrays", "match", "parameters"]}, "concat_space": {"spacing": "one"}, "array_indentation": true, "not_operator_with_successor_space": false, "function_typehint_space": true, "logical_operators": true, "no_singleline_whitespace_before_semicolons": true, "cast_spaces": {"space": "single"}, "modernize_types_casting": true, "fully_qualified_strict_types": true, "single_space_around_construct": true, "blank_line_before_statement": {"statements": ["continue", "return", "if", "for", "foreach"]}, "ordered_imports": {"sort_algorithm": "alpha"}, "binary_operator_spaces": {"default": "single_space"}, "operator_linebreak": {"only_booleans": true, "position": "beginning"}, "blank_line_after_opening_tag": true, "whitespace_after_comma_in_array": true, "no_multiple_statements_per_line": false, "ordered_traits": {"case_sensitive": true}, "compact_nullable_type_declaration": true, "method_chaining_indentation": true, "statement_indentation": true, "ternary_operator_spaces": true, "object_operator_without_whitespace": true, "no_space_around_double_colon": true, "single_quote": {"strings_containing_single_quote_chars": true}, "single_line_empty_body": false}}