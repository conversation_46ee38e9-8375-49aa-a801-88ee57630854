#!/bin/sh
echo "Git pre-commit..."

# Check if Docker container 'api.sh' is running
if ! docker ps | grep -q api.sh; then
    echo "Docker container 'api.sh' is not running. Please run 'docker-compose up -d' to start it."
    exit 1
fi

# Check if Laravel Pint is installed
if [ ! -f ./vendor/bin/pint ]; then
    echo "Laravel Pint is not installed. Please run 'composer install' to install it."
    exit 1
fi

# Check if Pint configuration file is present
if [ ! -f ./pint.json ]; then
    echo "Pint configuration file 'pint.json' is not present. Please create it."
    exit 1
fi

# Run Laravel Pint --test in api.sh docker container to check for errors
echo "Running Laravel Pint..."
docker exec -t api.sh ./vendor/bin/pint --test

# Check if Laravel Pint failed
if [ $? -ne 0 ]; then
    echo "Laravel Pint failed. Please fix the errors before committing."
    exit 1
fi
