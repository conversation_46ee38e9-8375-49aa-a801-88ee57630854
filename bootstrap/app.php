<?php

use App\Enums\LogGroupEnum;
use App\Exceptions\HttpException;
use App\Exceptions\QueryException;
use App\Extensions\Logger;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException as BaseQueryException;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Laravel\Passport\Exceptions\OAuthServerException as PassportOAuthServerException;
use Lara<PERSON>\Passport\Http\Middleware\CheckClientCredentials;
use Laravel\Passport\Http\Middleware\CheckForAnyScope;
use Lara<PERSON>\Passport\Http\Middleware\CheckScopes;
use League\OAuth2\Server\Exception\OAuthServerException as LeagueOAuthServerException;
use Psr\Log\LogLevel;
use Symfony\Component\HttpFoundation\Exception\SuspiciousOperationException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        commands: __DIR__ . '/../routes/console.php',
        channels: __DIR__ . '/../routes/channels.php',
        then: function () {
            Route::prefix('api')
                ->middleware(['api', 'throttle:api'])
                ->group(base_path('routes/api.php'));

            Route::prefix('api')
                ->middleware(['api', 'throttle:api'])
                ->group(base_path('routes/admin.php'));

            Route::prefix('api')
                ->middleware(['api', 'throttle:api'])
                ->group(base_path('routes/dme.php'));

            Route::prefix('api')
                ->middleware(['api', 'throttle:api'])
                ->group(base_path('routes/mfr.php'));

            Route::prefix('api')
                ->middleware(['api', 'throttle:api'])
                ->group(base_path('routes/hcp.php'));

            Route::prefix('api')
                ->middleware(['api', 'throttle:api'])
                ->group(base_path('routes/patient.php'));

            Route::prefix('api')
                ->middleware(['api', 'throttle:api'])
                ->group(base_path('routes/lead.php'));

            /* external api */
            Route::prefix('api/v1')
                ->middleware(['api', 'throttle:ext-api', 'enable_external_scopes'])
                ->name('ext.api.')
                ->group(base_path('routes/ext.php'));

            Route::prefix('api/v1')
                ->middleware(['api', 'throttle:ext-api', 'enable_external_scopes'])
                ->name('ext.api.')
                ->group(base_path('routes/ext-mfr.php'));

            Route::prefix('api/v1')
                ->middleware(['api', 'throttle:ext-api', 'enable_external_scopes'])
                ->name('ext.api.')
                ->group(base_path('routes/ext-dme.php'));
        },
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->redirectGuestsTo(function () {
            return response()->json(['message' => 'Unauthenticated.'], Response::HTTP_UNAUTHORIZED);
        });
        $middleware->group('web', []);
        $middleware->prependToGroup('api', [
            App\Http\Middleware\SetTokenExpiration::class,
        ]);
        $middleware->alias([
            'guest' => App\Http\Middleware\RedirectIfAuthenticated::class,
            'organization' => App\Http\Middleware\OrganizationContext::class,
            'horizonBasicAuth' => App\Http\Middleware\HorizonBasicAuthMiddleware::class,
            'scopes' => CheckScopes::class,
            'scope' => CheckForAnyScope::class,
            'client' => CheckClientCredentials::class,
            'enable_external_scopes' => App\Http\Middleware\EnableExternalScopes::class,
            'distributor-admin' => App\Http\Middleware\EnsureUserIsDistributorAdministrator::class,
            'manufacturer-sales-dashboard' => App\Http\Middleware\Manufacturer\SalesDashboardMiddleware::class,
            'cognito' => App\Http\Middleware\CognitoAuthenticate::class,
            'cognito-scope' => App\Http\Middleware\CheckCognitoScopes::class,
            'validate-insurance-card-token' => App\Http\Middleware\ValidateInsuranceCardRequestToken::class,
            'validate-lead-insurance-card-token' => App\Http\Middleware\ValidateLeadInsuranceCardRequestToken::class,
        ]);
        $middleware->throttleWithRedis();
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->stopIgnoring(AuthorizationException::class);
        $exceptions->stopIgnoring(SuspiciousOperationException::class);

        // NOTE: do not report AuthenticationException on route /api/me
        if (!request()->routeIs('me.show')) {
            $exceptions->stopIgnoring(AuthenticationException::class);
        }

        $exceptions->level(SuspiciousOperationException::class, LogLevel::ALERT);

        /**
         * NOTE: Brief explanation of this workaround:
         * There is an issue when Laravel throws a QueryException or its inheritors
         * (e.g., UniqueConstraintViolationException, DeadlockException),
         * these exceptions contain sensitive PII data in the clean SQL queries with bindings.
         * That's why we are mapping those Exceptions using our App\Exceptions\QueryException and formatting the message.
         * Removed $bindings - to prevent exposing sensitive PII data in the logs or Sentry.
         * An anonymous empty Exception for $previous - for the same reason, as there was previously a PDOException with a clean SQL query that we are not allowed to expose.
         */
        if (config('database.hide_raw_sql_in_exceptions')) {
            $exceptions->map(function (BaseQueryException $exception) {
                return new BaseQueryException(
                    connectionName: $exception->getConnectionName(),
                    sql: $exception->getSql(),
                    bindings: [],
                    previous: new QueryException($exception->getPrevious()),
                );
            });
        }

        // log exceptions
        $exceptions->report(function (AuthorizationException $e) {
            Logger::warning($e->getMessage(), LogGroupEnum::AUTH, [
                'url' => request()->fullUrl(),
                'user_id' => getUser()?->id,
                'user_email' => getUser()?->email,
            ]);
        })->stop();
        $exceptions->report(function (AuthenticationException $e) {
            Logger::info($e->getMessage(), LogGroupEnum::AUTH, [
                'url' => request()->fullUrl(),
            ]);
        })->stop();
        $exceptions->report(function (PassportOAuthServerException|LeagueOAuthServerException $e) {
            // code 9 means access_denied and happens with invalid credentials
            // code 8 means invalid_request and happens with invalid refresh token
            // code 6 means invalid_grant and happens with invalid credentials
            // code 4 means invalid_client and happens with invalid client_id
            if (in_array($e->getCode(), [9, 6, 8, 4])) {
                Logger::info($e->getMessage(), LogGroupEnum::AUTH, [
                    'url' => request()->fullUrl(),
                ]);
            } else {
                Logger::warning($e->getMessage(), LogGroupEnum::AUTH, [
                    'url' => request()->fullUrl(),
                ]);
            }
        })->stop();

        $exceptions->render(function (AuthenticationException $e, Request $request) {
            $isAccept = Str::contains($request->header('Accept') ?? '', ['/json', '+json']);

            if (in_array($request->getMethod(), ['POST', 'PUT', 'PATCH']) && !$isAccept) {
                return response()->json([
                    'message' => $e->getMessage(),
                    'details' => 'Headers: \'Accept\' is required to be application/json',
                ], Response::HTTP_UNSUPPORTED_MEDIA_TYPE);
            }

            return response()->json(['message' => $e->getMessage()], Response::HTTP_UNAUTHORIZED);
        });
        $exceptions->render(function (ThrottleRequestsException $e) {
            return response()->json(['message' => $e->getMessage()], $e->getStatusCode());
        });
        $exceptions->render(function (HttpException|MethodNotAllowedHttpException $e) {
            return response()->json(['message' => $e->getMessage()], $e->getStatusCode());
        });
        $exceptions->render(function (ModelNotFoundException $e) {
            $model = Str::afterLast($e->getModel(), '\\');

            return response()->json(
                [
                    'message' => 'No query results in model: ' . $model . ' ids: ' . implode(',', $e->getIds()),
                ],
                Response::HTTP_NOT_FOUND,
            );
        });
    })
    ->create();
