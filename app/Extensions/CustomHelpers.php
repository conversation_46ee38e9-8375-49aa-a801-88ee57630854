<?php

use App\Enums\PortalEnum;
use App\Models\BaseModel;
use App\Models\Lead;
use App\Models\Patient;
use App\Models\User;
use Database\Seeders\MessageConfigurationSeeder;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Testing\Fakes\BatchFake;
use Illuminate\Support\ValidatedInput;

if (!function_exists('splitRequestIntoParts')) {
    /**
     * Extract fields from main
     *
     * @param ValidatedInput $safe
     * @param array $fields
     *
     * @return array
     */
    function splitRequestIntoParts(ValidatedInput $safe, array $fields): array
    {
        $parsed = [
            'main' => $safe->except($fields),
        ];

        foreach ($fields as $field) {
            $filtered = $safe->only($field);
            $parsed[$field] = empty($filtered)
                ? []
                : $filtered[$field];
        }

        return $parsed;
    }
}

if (!function_exists('perPage')) {
    /**
     * Retrurns per page value from request
     */
    function perPage()
    {
        return request('per_page', BaseModel::PER_PAGE);
    }
}

if (!function_exists('getUser')) {
    /**
     * Get the currently authenticated user.
     *
     * @param null|string $guard
     *
     * @return null|User|Authenticatable
     */
    function getUser(?string $guard = null): User|Authenticatable|null
    {
        if ($guard === null && request()->attributes->get('auth_guard') !== null) {
            $guard = request()->attributes->get('auth_guard');
        }

        return auth($guard)->user();
    }
}

if (!function_exists('todayTz')) {
    /**
     * Get the current date in the application's timezone.
     */
    function todayTz(): Carbon
    {
        return Carbon::today(config('app.sh_timezone'));
    }
}

if (!function_exists('isPortal')) {
    /**
     * Check if the user is authenticated in the specified portal.
     */
    function isPortal(PortalEnum $portal): bool
    {
        $token = request()->attributes->get('cognito_jwt');
        $groups = $token->{'cognito:groups'} ?? [];

        if (!$token) {
            if (app()->runningInConsole()) {
                return $portal === PortalEnum::CONSOLE;
            }

            return $portal === PortalEnum::PUBLIC;
        }

        $scope = match ($portal) {
            PortalEnum::ADMIN => 'int-sha-scope',
            PortalEnum::DISTRIBUTOR => 'int-dme-scope',
            PortalEnum::MANUFACTURER => 'int-mfr-scope',
            PortalEnum::PROVIDER => 'int-hcp-scope',
            PortalEnum::EXTERNAL_DISTRIBUTOR => 'ext-dme-scope',
            PortalEnum::EXTERNAL_MANUFACTURER => 'ext-mfr-scope',
        };

        return in_array($scope, $groups);
    }

    if (!function_exists('getMeroryPeakUsageInMb')) {
        function getMeroryPeakUsageInMb(): string
        {
            return round(memory_get_peak_usage() / 1024 / 1024, 2) . 'MB';
        }
    }
}

if (!function_exists('searchInMultidimensionalArray')) {
    /**
     * Search for a value in a multidimensional array.
     */
    function searchInMultidimensionalArray(array $array, string $key, $value): ?array
    {
        foreach ($array as $element) {
            if (is_array($element)) {
                $result = searchInMultidimensionalArray($element, $key, $value);

                if ($result !== null) {
                    return $result;
                }
            } elseif (isset($array[$key]) && $array[$key] == $value) {
                return $array;
            }
        }

        return null;
    }
}

if (!function_exists('awaitingBatch')) {
    function awaitingBatch(mixed $batch, int $maxSeconds = 60): void
    {
        if (!$batch instanceof BatchFake) {
            $times = 0;
            while (true) {
                if ($times >= $maxSeconds) {
                    break;
                }

                $batch = Bus::findBatch($batch->id);

                if ($batch->finished()) {
                    break;
                }

                sleep(1);
                $times++;
            }
        }
    }
}

/**
 * Generate a temporary resubscribe URL with encrypted email token.
 */
if (! function_exists('generateResubscribeUrl')) {
    function generateResubscribeUrl($email): string|bool
    {

        if ($email) {

            $encryptedEmail = Crypt::encryptString($email);

            $signedUrl = URL::temporarySignedRoute(
                'resubscribe',
                now()->addWeek(),
                ['token' => $encryptedEmail],
            );

            $queryParms = parse_url($signedUrl, PHP_URL_QUERY);

            return config('app.distributor_client_domain') . '/subscribe?' . $queryParms;
        }

        return false;

    }
}

/**
 * Generate a temporary unsubscribe URL with encrypted email token.
 */
if (! function_exists('generateUnsubscribeUrl')) {
    function generateUnsubscribeUrl(object $notifiable): string|bool
    {
        if ($notifiable instanceof Lead || $notifiable instanceof Patient) {
            $email = $notifiable?->email ?? null;

            if (!$email) {
                return false;
            }

            $encryptedEmail = Crypt::encryptString($email);

            $signedUrl = URL::temporarySignedRoute(
                'unsubscribe',
                now()->addWeek(),
                ['token' => $encryptedEmail],
            );

            $queryParms = parse_url($signedUrl, PHP_URL_QUERY);

            return config('app.distributor_client_domain') . '/unsubscribe?' . $queryParms;
        }

        return false;
    }
}

/**
 * Generate a temporary unsubscribe URL with encrypted email token.
 */
if (! function_exists('generateTemporaryPassword')) {
    function generateTemporaryPassword(int $length = 12): string
    {
        // Cognito default policy: upper, lower, number, special char
        $upper = chr(random_int(65, 90)); // A-Z
        $lower = chr(random_int(97, 122)); // a-z
        $number = chr(random_int(48, 57)); // 0-9
        $special = substr('!@#$%^&*()-_=+[]{};:,.<>?', random_int(0, 20), 1);

        // Fill the rest randomly
        $all = str_shuffle(
            $upper .
            $lower .
            $number .
            $special .
            Str::random($length - 4),
        );

        // Shuffle again for extra randomness
        return str_shuffle($all);
    }
}

/**
 * Seed message configurations for a distributor or all distributors.
 */
if (! function_exists('seedMessageConfigurations')) {
    function seedMessageConfigurations(?int $distributorId = null): void
    {
        $seeder = new MessageConfigurationSeeder($distributorId);
        $seeder->run();
    }
}

if (!function_exists('todayDateTimeTz')) {
    /**
     * Get the current dateTime in the application's timezone.
     */
    function todayDateTimeTz(): Carbon
    {
        return Carbon::now(config('app.sh_timezone'));
    }
}

if (!function_exists('isValidDateTime')) {
    /**
     * Check whether the given value is a valid timestamp.
     */
    function isValidDateTime(string $value): bool
    {
        $value = strtoupper($value);
        $formats = [
            'm/d/Y h:i:s A',
            'm/d/Y g:i:s A',
            'm/d/Y H:i:s',
            'm/d/Y g:i:s',
            'm/d/Y h:i:s',
        ];

        foreach ($formats as $format) {
            try {
                $dt = Carbon::createFromFormat($format, $value);

                // Double check re-formatting to make sure exact match
                if ($dt && $dt->format($format) === $value) {
                    return true;
                }
            } catch (Exception $e) {
                continue;
            }
        }

        return false;
    }
}

if (!function_exists('parseCreatedDate')) {
    function parseCreatedDate(?string $value): Carbon
    {
        if (!empty($value)) {
            if (isValidDateTime($value)) {
                return Carbon::parse($value);
            }

            $dateOnly = strtok($value, ' ');
            try {
                return Carbon::createFromFormat('m/d/Y', $dateOnly)->startOfDay();
            } catch (Exception $e) {
                // fall through
            }
        }

        return now()->startOfDay();
    }
}
