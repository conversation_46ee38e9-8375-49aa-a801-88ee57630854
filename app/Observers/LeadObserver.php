<?php

namespace App\Observers;

use App\Enums\ActivityLogTypeEnum;
use App\Enums\DistributorCampaignStatusEnum;
use App\Enums\LeadStatusEnum;
use App\Enums\LogGroupEnum;
use App\Extensions\Logger;
use App\Jobs\Leads\SendLeadConvertedToOrderEventToFacebook;
use App\Jobs\Leads\SendLeadQualifiedEventToFacebook;
use App\Jobs\Leads\SendLeadUnqualifiedEventToFacebook;
use App\Models\Lead;
use App\Models\LeadCampaignCancellationHistory;
use App\Notifications\Sms\CommunicationStart;
use App\Notifications\Sms\CommunicationStop;
use App\Services\ShuttleHealth\ActivityLogger;
use ValueError;

/**
 * This observer is responsible for logging the lead updates and sending the communication start/stop SMS notifications.
 */
class LeadObserver
{
    public function saving(Lead $lead): void
    {
        // Check if the 'status' field is being changed and the new status is CONVERTED
        if ($lead->isDirty('status') && $lead->status === LeadStatusEnum::CONVERTED) {
            $currentUser = getUser();

            if ($currentUser) {
                // Only update if the assigned_user_id is not already the current user.
                // This covers cases where it's null or assigned to a different user.
                if ($lead->assigned_user_id !== $currentUser->id) {
                    $lead->assigned_user_id = $currentUser->id;
                }
            }
        }
    }

    public function updated(Lead $lead): void
    {
        if (!app()->runningInConsole() && $lead->utm_source == 'facebook') {
            $ipAddress = request()->ip();
            $userAgent = request()->userAgent();

            $leadStatus = $lead->status;

            try {
                match ($lead->status) {
                    LeadStatusEnum::CANCELED => SendLeadUnqualifiedEventToFacebook::dispatch($lead, $ipAddress, $userAgent),
                    LeadStatusEnum::CONVERTED => SendLeadConvertedToOrderEventToFacebook::dispatch($lead, $ipAddress, $userAgent),
                    default => null,
                };

                if (
                    $lead->wasChanged('qualified_date')
                    && is_null($lead->getOriginal('qualified_date'))
                    && !is_null($lead->qualified_date)
                ) {
                    SendLeadQualifiedEventToFacebook::dispatch($lead, $ipAddress, $userAgent);
                }
            } catch (ValueError $e) {
                Logger::error('Invalid lead status for Facebook event dispatch', LogGroupEnum::FACEBOOK_PIXEL, [
                    'leadId' => $lead->id,
                    'status' => $lead->status,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        if ($lead->wasChanged('qualified_date')) {
            ActivityLogger::write($lead, ActivityLogTypeEnum::UPDATE_QUALIFIED_DATE, [
                'from' => $lead->getOriginal('qualified_date')?->toDateString(),
                'to' => $lead->qualified_date?->toDateString(),
            ]);
        }

        if ($lead->wasChanged('communications')
            || $lead->wasChanged('sms_enabled')
            || $lead->wasChanged('email_enabled')
            || $lead->wasChanged('call_enabled')) {

            // Make sure campaign is active before proceeding
            if ($lead->distributorCampaign->status === DistributorCampaignStatusEnum::ACTIVE) {
                $fromNumber = $lead->distributorCampaign->distributor?->assigned_fax;

                // Check if any of the communication options are enabled and there is an opt-in text
                $distributorId = $lead->distributorCampaign->distributor_id;

                if ($lead->communications || $lead->sms_enabled || $lead->email_enabled || $lead->call_enabled) {
                    $lead->notify(new CommunicationStart($fromNumber), true);
                }
                // If communications are disabled and there is an opt-out text, send CommunicationStop
                elseif (!$lead->communications && !$lead->sms_enabled && !$lead->email_enabled && !$lead->call_enabled) {
                    $lead->notify(new CommunicationStop($fromNumber), true);
                } else {
                    Logger::warning('There is no opt in or opt out text for such campaign', LogGroupEnum::SIGNAL_WIRE, [
                        'campaignName' => $lead->distributorCampaign->name,
                        'distributorId' => $lead->distributorCampaign->distributor_id,
                    ]);
                }
            }
        }

        if ($lead->wasChanged('status')) {
            ActivityLogger::write($lead, ActivityLogTypeEnum::UPDATE_STATUS, [
                'from' => $lead->getOriginal('status'),
                'to' => $lead->status,
            ]);

            // Record cancellation history when lead is cancelled
            if ($lead->status === LeadStatusEnum::CANCELED && $lead->getOriginal('status') !== LeadStatusEnum::CANCELED) {
                LeadCampaignCancellationHistory::create([
                    'lead_id' => $lead->id,
                    'distributor_campaign_id' => $lead->distributor_campaign_id,
                    'canceled_reason' => $lead->canceled_reason,
                    'canceled_date' => $lead->canceled_date ?? now()->toDateString(),
                    'canceled_by' => getUser()?->id,
                ]);
            }
        }

        if ($lead->wasChanged('first_name')) {
            ActivityLogger::write($lead, ActivityLogTypeEnum::UPDATE_LEAD_FIRST_NAME, [
                'from' => $lead->getOriginal('first_name'),
                'to' => $lead->first_name,
            ]);
        }

        if ($lead->wasChanged('last_name')) {
            ActivityLogger::write($lead, ActivityLogTypeEnum::UPDATE_LEAD_LAST_NAME, [
                'from' => $lead->getOriginal('last_name'),
                'to' => $lead->last_name,
            ]);
        }

    }

    public function created(Lead $lead): void
    {
        // Ensure the campaign is active before proceeding
        if ($lead->distributorCampaign->status === DistributorCampaignStatusEnum::ACTIVE) {
            $fromNumber = $lead->distributorCampaign->distributor?->assigned_fax;

            if ($lead->communications || $lead->sms_enabled || $lead->email_enabled || $lead->call_enabled) {
                $lead->notify(new CommunicationStart($fromNumber));
            } else {
                Logger::warning('There is no opt in or opt out text for such campaign', LogGroupEnum::SIGNAL_WIRE, [
                    'campaignName' => $lead->distributorCampaign->name,
                    'distributorId' => $lead->distributorCampaign->distributor_id,
                ]);
            }
        }
    }
}
