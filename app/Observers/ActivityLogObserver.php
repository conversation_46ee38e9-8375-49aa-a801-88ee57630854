<?php

namespace App\Observers;

use App\Models\ActivityLog;
use Illuminate\Support\Carbon;

/**
 * This observer is responsible for storing the activity log updates to history.
 */
class ActivityLogObserver
{
    public function updated(ActivityLog $log): void
    {
        $oldValues = [];
        $newValues = [];

        if ($log->wasChanged('metadata')) {
            $oldValues['metadata'] = $log->getOriginal('metadata');
            $newValues['metadata'] = $log->metadata;
        }

        if ($log->wasChanged('activity_at')) {
            $oldValues['activity_at'] = $log->getOriginal('activity_at')?->toDateTimeString();
            $newValues['activity_at'] = $log->activity_at?->toDateTimeString();
        }

        if (!empty($oldValues) || !empty($newValues)) {
            $log->history()->create([
                'old_values' => $oldValues,
                'new_values' => $newValues,
                'activity_at' => Carbon::now(),
                'user_id' => getUser()->id,
            ]);
        }
    }

    public function deleting(ActivityLog $log): void
    {
        $admin = getUser(); // Get the current admin user
        $log->history()->create([
            'old_values' => [
                'metadata' => $log->metadata,
                'activity_at' => $log->activity_at?->toDateTimeString(),
                'deleted_at' => Carbon::now()->toDateTimeString(),
                'deleted_by' => $admin ? ['id' => $admin->id, 'name' => $admin->getFullName()] : null,
            ],
            'new_values' => null,
            'activity_at' => Carbon::now(),
            'user_id' => $admin?->id,
        ]);
    }
}
